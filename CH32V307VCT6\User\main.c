/********************************** (C) COPYRIGHT *******************************
* File Name          : main.c
* Author             : WCH
* Version            : V1.0.0
* Date               : 2021/06/06
* Description        : Main program body.
*********************************************************************************
* Copyright (c) 2021 Nanjing Qinheng Microelectronics Co., Ltd.
* Attention: This software (modified or not) and binary are used for 
* microcontroller manufactured by Nanjing Qinheng Microelectronics.
*******************************************************************************/

/*
 *@Note
 USART Print debugging routine:
 USART1_Tx(PA9).
 This example demonstrates using USART1(PA9) as a print debug port output.

*/

#include "debug.h"
#include "max30102.h"
#include "ds18b20.h"
#include "ch32v30x_gpio.h"
#include <stdarg.h>  // 用于可变参数函数
#include <stdio.h>   // 用于vsnprintf
#include <string.h>  // 用于strlen函数

// AS608指纹模块相关定义
#define AS608_UART_BAUD 57600  // AS608默认波特率
#define AS608_PACKET_HEAD 0xEF01  // 包头
#define AS608_CMD_GET_IMAGE 0x01  // 获取图像命令
#define AS608_CMD_DETECT_FINGER 0x02  // 检测手指命令
#define AS608_ACK_SUCCESS 0x00  // 成功应答
#define AS608_ACK_NO_FINGER 0x02  // 无手指应答

// AS608引脚定义 (使用UART2)
#define AS608_UART_TX_PIN GPIO_Pin_2  // PA2 - UART2_TX
#define AS608_UART_RX_PIN GPIO_Pin_3  // PA3 - UART2_RX
#define AS608_UART_PORT GPIOA
#define AS608_UART USART2

#define MAX_BRIGHTNESS 255
#define INTERRUPT_REG 0X00

/*  VCC<->3.3V
    GND<->GND
    SCL<->PB7
    SDA<->PB8
    INT<->PB9*/

uint32_t aun_ir_buffer[500];     //IR LED   ��������ݣ����ڼ���Ѫ��
int32_t n_ir_buffer_length;    //���ݳ���
uint32_t aun_red_buffer[500];  //Red LED    ������ݣ����ڼ������������Լ���������
int32_t n_sp02; //SPO2ֵ
int8_t ch_spo2_valid;   //������ʾSP02�����Ƿ���Ч��ָʾ��
int32_t n_heart_rate;   //����ֵ
int8_t  ch_hr_valid;    //������ʾ���ʼ����Ƿ���Ч��ָʾ��

uint8_t Temp;

uint32_t un_min, un_max, un_prev_data;
int i;
int32_t n_brightness;
float f_temp;
//u8 temp_num=0;
u8 temp[6];
u8 str[100];
u8 dis_hr=0,dis_spo2=0;
// 数据平滑处理变量
u8 last_valid_hr=0, last_valid_spo2=0;  // 上一次有效值
u8 hr_invalid_count=0, spo2_invalid_count=0;  // 无效数据计数器

// 快速响应优化变量
u8 fast_response_enabled = 1;  // 快速响应开关

// AS608指纹模块相关变量
u8 finger_detected = 0;  // 手指检测状态：0-无手指，1-有手指
u8 last_finger_state = 0;  // 上次手指状态
u8 monitoring_enabled = 0;  // 监测使能：0-禁用，1-启用
u32 finger_check_timer = 0;  // 手指检测定时器
// 心率移动平均滤波器
u8 hr_buffer[5] = {0};  // 存储最近5个心率值
u8 hr_buffer_index = 0;  // 缓冲区索引
u8 hr_buffer_count = 0;  // 有效数据计数

// 心率波形图相关变量
#define WAVEFORM_WIDTH 120   // 波形图宽度
#define WAVEFORM_HEIGHT 60   // 波形图高度（放大到60像素增强清晰度）
#define WAVEFORM_X 4         // 波形图X起始位置
#define WAVEFORM_Y 60        // 波形图Y起始位置（上移利用简化后的空间）
u8 hr_waveform_buffer[WAVEFORM_WIDTH] = {0};  // 心率波形数据缓冲区
u8 waveform_index = 0;       // 波形数据索引
u16 waveform_update_counter = 0;  // 波形更新计数器

// 蓝牙波形数据传输相关变量
#define WAVEFORM_SEND_INTERVAL 10  // 每10个波形点发送一次数据包
u8 waveform_send_counter = 0;      // 发送计数器
u8 waveform_send_buffer[WAVEFORM_SEND_INTERVAL] = {0};  // 发送缓冲区

// DS18B20温度传感器状态管理
u8 ds18b20_initialized = 0;       // DS18B20初始化状态标志
u8 ds18b20_working = 0;           // DS18B20工作状态标志
u8 system_status_displayed = 0;   // 系统状态显示标志
u32 status_display_timer = 0;     // 状态显示计时器

// 温度变化检测相关变量
float last_stable_temperature = 0;    // 上次稳定温度值
u8 temperature_change_detected = 0;   // 温度变化检测标志
u32 message_display_timer = 0;        // 消息显示计时器
u8 message_displayed = 0;             // 消息显示状态标志
u8 force_display_trigger = 0;        // 强制显示触发标志
#define TEMP_CHANGE_THRESHOLD 2       // 温度变化阈值（0.2度 = 2个单位，更敏感）
#define MESSAGE_DISPLAY_DURATION 3000 // 消息显示持续时间（3秒）

// 蓝牙UART配置
#define BLUETOOTH_UART USART2      // 使用UART2连接蓝牙模块
#define BLUETOOTH_BAUDRATE 9600    // 蓝牙模块波特率
#define ENABLE_BLUETOOTH_COMMUNICATION 1  // 蓝牙通信开关，1-开启，0-关闭

// 显示优化变量
u8 last_dis_hr = 255, last_dis_spo2 = 255;     // 上次显示的数值（用于防闪烁）
u16 last_dis_temp = 0xFFFF;                    // 上次显示的温度
u16 display_update_counter = 0;                // 显示更新计数器
u8 display_refresh_flag = 1;                   // 显示刷新标志

// 心率模拟相关变量
#define ENABLE_HR_SIMULATION 1                 // 心率模拟开关，1-开启，0-使用真实数据
#define HR_BASE_VALUE 75                       // 基础心率值
#define HR_MIN_VALUE 55                        // 最小心率值
#define HR_MAX_VALUE 105                       // 最大心率值（限制到105）
#define HR_CHANGE_PROBABILITY 95               // 变化概率（百分比）- 提高跳变频率
#define HR_MAX_CHANGE 3                        // 最大单次变化量（减小到3）
u8 simulated_heart_rate = HR_BASE_VALUE;       // 模拟心率值
u32 random_seed = 12345;                       // 随机数种子

// 增加更多心率影响因子
u8 stress_level = 0;                           // 压力水平（0-10）
u8 activity_intensity = 0;                     // 活动强度（0-10）
u8 fatigue_level = 0;                          // 疲劳水平（0-10）
u16 daily_cycle_counter = 0;                   // 日常周期计数器

// 数据异常报警相关定义
#define TEMP_HIGH_THRESHOLD 400                // 高温报警阈值：40.0度（*10）
#define TEMP_LOW_THRESHOLD 350                 // 低温报警阈值：35.0度（*10）
#define HR_HIGH_THRESHOLD 125                  // 心率过高报警阈值：125次/分
#define HR_LOW_THRESHOLD 60                    // 心率过低报警阈值：60次/分
u8 last_temp_alarm_type = 0;                   // 上次温度报警类型：0-无，1-高温，2-低温
u8 last_hr_alarm_type = 0;                     // 上次心率报警类型：0-无，1-过高，2-过低

// 心率模拟模式（扩展更多模式）
#define HR_MODE_SLEEP 0                        // 睡眠模式：55-65 BPM
#define HR_MODE_RESTING 1                      // 静息模式：60-75 BPM
#define HR_MODE_NORMAL 2                       // 正常模式：70-85 BPM
#define HR_MODE_ACTIVE 3                       // 活跃模式：80-100 BPM
#define HR_MODE_EXERCISE 4                     // 运动模式：95-120 BPM
u8 hr_simulation_mode = HR_MODE_NORMAL;        // 当前模拟模式
#define MAX_INVALID_COUNT 3  // 最大无效数据容忍次数
#define ENABLE_DATA_SMOOTH_DEBUG 0  // 数据平滑调试开关，1-开启，0-关闭
#define HR_SMOOTH_LEVEL 3  // 心率平滑级别：1-轻度，2-中度，3-重度
#define ENABLE_WAVEFORM_BLUETOOTH 1  // 波形蓝牙传输开关，1-开启，0-关闭

// 颜色定义（16位RGB565格式）- 优化对比度
#define COLOR_WHITE    0xFFFF  // 白色
#define COLOR_BLACK    0x0000  // 黑色
#define COLOR_RED      0xF800  // 红色
#define COLOR_GREEN    0x07E0  // 绿色
#define COLOR_BLUE     0x001F  // 蓝色
#define COLOR_YELLOW   0xFFE0  // 黄色
#define COLOR_CYAN     0x07FF  // 青色
#define COLOR_MAGENTA  0xF81F  // 洋红色
#define COLOR_GRAY     0x8410  // 灰色

// 增强可视性的颜色定义
#define COLOR_BRIGHT_RED    0xF81F  // 亮红色
#define COLOR_BRIGHT_GREEN  0x07FF  // 亮绿色
#define COLOR_BRIGHT_BLUE   0x3F1F  // 亮蓝色
#define COLOR_ORANGE        0xFD20  // 橙色
#define COLOR_PURPLE        0x8010  // 紫色
#define COLOR_DARK_GRAY     0x4208  // 深灰色
#define COLOR_LIGHT_GRAY    0xC618  // 浅灰色

// 显示优化配置
#define ENABLE_DISPLAY_OPTIMIZATION 1  // 显示优化开关
#define DISPLAY_UPDATE_INTERVAL 1      // 显示更新间隔（提高刷新频率以快速响应）
#define ENABLE_ANTI_FLICKER 1          // 防闪烁开关


#define ST7735_SS_GPIO      GPIO_Pin_0
#define ST7735_SCK_GPIO     GPIO_Pin_1
#define ST7735_MOSI_GPIO    GPIO_Pin_2
#define ST7735_MISO_GPIO    GPIO_Pin_3


#define ST7735S_RST_GPIO    GPIO_Pin_4
#define ST7735S_DC_GPIO     GPIO_Pin_5

#define BUZZER_GPIO_PORT GPIOC
#define BUZZER_GPIO_PIN  GPIO_Pin_13
#define BUZZER_ON()      GPIO_WriteBit(BUZZER_GPIO_PORT, BUZZER_GPIO_PIN, Bit_RESET)  // 假设蜂鸣器低电平有效
#define BUZZER_OFF()     GPIO_WriteBit(BUZZER_GPIO_PORT, BUZZER_GPIO_PIN, Bit_SET)

/* Global typedef */

/* Global define */

/* Global Variable */

/*********************************************************************
 * @fn      AS608_Init
 *
 * @brief   初始化AS608指纹模块
 */
void AS608_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    USART_InitTypeDef USART_InitStructure = {0};

    // 使能UART2和GPIOA时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // 配置UART2 TX引脚 (PA2)
    GPIO_InitStructure.GPIO_Pin = AS608_UART_TX_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(AS608_UART_PORT, &GPIO_InitStructure);

    // 配置UART2 RX引脚 (PA3)
    GPIO_InitStructure.GPIO_Pin = AS608_UART_RX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(AS608_UART_PORT, &GPIO_InitStructure);

    // 配置UART2
    USART_InitStructure.USART_BaudRate = AS608_UART_BAUD;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(AS608_UART, &USART_InitStructure);

    // 使能UART2
    USART_Cmd(AS608_UART, ENABLE);

    printf("AS608 fingerprint module initialized\r\n");
}

/*********************************************************************
 * @fn      AS608_DetectFinger
 *
 * @brief   检测手指是否放在指纹模块上
 *
 * @return  1-检测到手指，0-未检测到手指
 */
u8 AS608_DetectFinger(void)
{
    // 为了演示，我们使用一个简单的定时器模拟手指检测
    static u32 demo_timer = 0;
    static u8 demo_state = 0;
    demo_timer++;

    // 每5秒切换一次状态（模拟手指接触和移开）
    if(demo_timer > 5000) {  // 假设每毫秒调用一次
        demo_timer = 0;
        demo_state = !demo_state;  // 切换状态
        return demo_state;
    }

    return demo_state;  // 返回当前模拟状态
}

/*********************************************************************
 * @fn      ProcessFingerDetection
 *
 * @brief   处理手指检测逻辑
 */
void ProcessFingerDetection(void)
{
    finger_check_timer++;

    // 每100ms检测一次手指状态
    if(finger_check_timer >= 100) {
        finger_check_timer = 0;

        finger_detected = AS608_DetectFinger();

        // 检测手指状态变化
        if(finger_detected != last_finger_state) {
            if(finger_detected) {
                // 检测到手指，启用监测
                monitoring_enabled = 1;
                printf("Finger detected - Health monitoring enabled\r\n");
                printf("手指检测到 - 开始生理数据监测\r\n");
            } else {
                // 手指移开，禁用监测
                monitoring_enabled = 0;
                printf("Finger removed - Health monitoring disabled\r\n");
                printf("手指移开 - 停止生理数据监测\r\n");
            }
            last_finger_state = finger_detected;
        }
    }
}

/*********************************************************************
 * @fn      SmoothDataFilter
 *
 * @brief   数据平滑处理函数，避免数据突然跳变为0
 *
 * @param   new_value - 新的数据值
 *          is_valid - 数据是否有效
 *          last_valid - 上一次有效值指针
 *          invalid_count - 无效数据计数器指针
 *          min_valid - 最小有效值
 *          max_valid - 最大有效值
 *          default_value - 无效时的默认值
 *
 * @return  处理后的数据值
 */
u8 SmoothDataFilter(u8 new_value, u8 is_valid, u8 *last_valid, u8 *invalid_count, u8 min_valid, u8 max_valid, u8 default_value)
{
    // 如果数据有效且在合理范围内
    if(is_valid && new_value >= min_valid && new_value <= max_valid) {
        // 检查是否有剧烈跳变（变化超过30%）
        if(*last_valid > 0) {
            u8 change_threshold = (*last_valid * 30) / 100;  // 30%变化阈值
            if(new_value > *last_valid + change_threshold || new_value < *last_valid - change_threshold) {
                // 剧烈跳变，使用渐变处理
                if(new_value > *last_valid) {
                    *last_valid = *last_valid + (change_threshold / 2);  // 渐变增加
                } else {
                    *last_valid = *last_valid - (change_threshold / 2);  // 渐变减少
                }
            } else {
                *last_valid = new_value;  // 正常更新
            }
        } else {
            *last_valid = new_value;  // 首次有效值
        }
        *invalid_count = 0;  // 重置无效计数器
        return *last_valid;
    } else {
        // 数据无效，增加无效计数器
        (*invalid_count)++;

        // 如果连续无效次数超过阈值，设为默认值
        if(*invalid_count > MAX_INVALID_COUNT) {
            *last_valid = default_value;
            return default_value;
        } else {
            // 保持上一次有效值
            return *last_valid;
        }
    }
}

/*********************************************************************
 * @fn      SmoothHeartRateFilter
 *
 * @brief   心率专用平滑处理函数，减少波动
 *          无效信息时直接显示0，不使用默认值
 *
 * @param   new_value - 新的心率值
 *          is_valid - 数据是否有效
 *          last_valid - 上一次有效值指针
 *          invalid_count - 无效数据计数器指针
 *
 * @return  处理后的心率值（无效时返回0）
 */
u8 SmoothHeartRateFilter(u8 new_value, u8 is_valid, u8 *last_valid, u8 *invalid_count)
{
    const u8 min_hr = 40;      // 最小心率
    const u8 max_hr = 150;     // 最大心率
    const u8 default_hr = 80;  // 默认心率

    // 如果数据有效且在合理范围内
    if(is_valid && new_value >= min_hr && new_value <= max_hr) {
        // 心率专用平滑处理，根据平滑级别调整参数
        if(*last_valid > 0) {
            // 根据平滑级别设置变化阈值
            u8 threshold_percent = (HR_SMOOTH_LEVEL == 1) ? 20 : (HR_SMOOTH_LEVEL == 2) ? 15 : 10;
            u8 change_threshold = (*last_valid * threshold_percent) / 100;
            u8 min_threshold = (HR_SMOOTH_LEVEL == 1) ? 5 : (HR_SMOOTH_LEVEL == 2) ? 3 : 2;
            if(change_threshold < min_threshold) change_threshold = min_threshold;

            if(new_value > *last_valid + change_threshold || new_value < *last_valid - change_threshold) {
                // 剧烈跳变，使用更平滑的渐变处理
                u8 step = change_threshold / 4;  // 更小的步长
                if(step < 1) step = 1;

                if(new_value > *last_valid) {
                    *last_valid = *last_valid + step;  // 缓慢增加
                } else {
                    *last_valid = *last_valid - step;  // 缓慢减少
                }
            } else {
                // 小幅变化，使用加权平均进一步平滑，权重根据平滑级别调整
                if(HR_SMOOTH_LEVEL == 1) {
                    *last_valid = (*last_valid * 1 + new_value) / 2;  // 50%旧值 + 50%新值
                } else if(HR_SMOOTH_LEVEL == 2) {
                    *last_valid = (*last_valid * 3 + new_value) / 4;  // 75%旧值 + 25%新值
                } else {
                    *last_valid = (*last_valid * 7 + new_value) / 8;  // 87.5%旧值 + 12.5%新值
                }
            }
        } else {
            *last_valid = new_value;  // 首次有效值
        }

        // 添加到移动平均滤波器
        hr_buffer[hr_buffer_index] = *last_valid;
        hr_buffer_index = (hr_buffer_index + 1) % 5;  // 循环索引
        if(hr_buffer_count < 5) hr_buffer_count++;

        // 计算移动平均值
        u16 sum = 0;
        for(u8 i = 0; i < hr_buffer_count; i++) {
            sum += hr_buffer[i];
        }
        u8 avg_value = sum / hr_buffer_count;

        *invalid_count = 0;  // 重置无效计数器
        return avg_value;  // 返回平均值而不是瞬时值
    } else {
        // 数据无效，增加无效计数器
        (*invalid_count)++;

        // 如果连续无效次数超过阈值，直接设为0
        if(*invalid_count > MAX_INVALID_COUNT) {
            *last_valid = 0;
            return 0;
        } else {
            // 保持上一次有效值
            return *last_valid;
        }
    }
}

/*********************************************************************
 * @fn      Bluetooth_UART_Init
 *
 * @brief   初始化蓝牙UART通信
 *          连接说明：
 *          蓝牙模块VCC -> 3.3V
 *          蓝牙模块GND -> GND
 *          蓝牙模块RX  -> PA2 (UART2_TX)
 *          蓝牙模块TX  -> PA3 (UART2_RX)
 *
 * @return  none
 */
void Bluetooth_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    // 使能UART2和GPIOA时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // 配置UART2_TX (PA2)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;  // 复用推挽输出
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置UART2_RX (PA3)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;  // 浮空输入
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置UART2参数
    USART_InitStructure.USART_BaudRate = BLUETOOTH_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;

    // 初始化UART2
    USART_Init(BLUETOOTH_UART, &USART_InitStructure);
    USART_Cmd(BLUETOOTH_UART, ENABLE);

    printf("Bluetooth UART2 initialized at %d baud\r\n", BLUETOOTH_BAUDRATE);
}

/*********************************************************************
 * @fn      Bluetooth_SendChar
 *
 * @brief   通过蓝牙UART发送单个字符
 *
 * @param   ch - 要发送的字符
 *
 * @return  none
 */
void Bluetooth_SendChar(char ch)
{
    // 等待发送缓冲区空
    while(USART_GetFlagStatus(BLUETOOTH_UART, USART_FLAG_TXE) == RESET);

    // 发送字符
    USART_SendData(BLUETOOTH_UART, (uint8_t)ch);
}

/*********************************************************************
 * @fn      Bluetooth_SendString
 *
 * @brief   通过蓝牙UART发送字符串
 *
 * @param   str - 要发送的字符串
 *
 * @return  none
 */
void Bluetooth_SendString(char *str)
{
    while(*str) {
        Bluetooth_SendChar(*str++);
    }
}

/*********************************************************************
 * @fn      Bluetooth_Printf
 *
 * @brief   通过蓝牙UART发送格式化字符串
 *
 * @param   format - 格式化字符串
 *          ... - 可变参数
 *
 * @return  none
 */
void Bluetooth_Printf(const char *format, ...)
{
    char buffer[128];  // 缓冲区
    va_list args;

    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    Bluetooth_SendString(buffer);
}

/*********************************************************************
 * @fn      SimpleRandom
 *
 * @brief   简单的线性同余随机数生成器
 *
 * @return  随机数值
 */
u32 SimpleRandom(void)
{
    random_seed = (random_seed * 1103515245 + 12345) & 0x7FFFFFFF;
    return random_seed;
}

/*********************************************************************
 * @fn      GetRandomInRange
 *
 * @brief   获取指定范围内的随机数
 *
 * @param   min - 最小值
 *          max - 最大值
 *
 * @return  范围内的随机数
 */
u8 GetRandomInRange(u8 min, u8 max)
{
    if(min >= max) return min;
    return min + (SimpleRandom() % (max - min + 1));
}

/*********************************************************************
 * @fn      SimulateHeartRate
 *
 * @brief   模拟心率数据，在60-100之间随机小规模跳变
 *
 * @return  模拟的心率值
 */
/*********************************************************************
 * @fn      CheckTemperatureAlarm
 *
 * @brief   检测温度异常并发送报警信号
 *
 * @param   temp_value - 当前温度值（*10，如385表示38.5度）
 *
 * @return  none
 */
void CheckTemperatureAlarm(u16 temp_value)
{
    u8 current_alarm_type = 0;  // 当前报警类型

    // 检测温度异常
    if(temp_value > TEMP_HIGH_THRESHOLD) {
        current_alarm_type = 1;  // 高温报警
    } else if(temp_value < TEMP_LOW_THRESHOLD) {
        current_alarm_type = 2;  // 低温报警
    } else {
        current_alarm_type = 0;  // 正常温度
    }

    // 处理报警状态变化 - 一旦异常立即发送报警信号
    if(current_alarm_type != last_temp_alarm_type) {
        if(current_alarm_type > 0) {
            // 检测到异常，立即发送报警信号
            printf("体温报警: 类型=%d, 体温=%.1f°C\r\n",
                   current_alarm_type, temp_value / 10.0f);
            SendTemperatureAlarmToBluetooth(current_alarm_type, temp_value);
        } else {
            // 温度恢复正常
            printf("体温正常: 体温=%.1f°C\r\n", temp_value / 10.0f);
        }
        last_temp_alarm_type = current_alarm_type;
    }

    // 如果当前仍处于异常状态，继续发送报警信号
    if(current_alarm_type > 0) {
        SendTemperatureAlarmToBluetooth(current_alarm_type, temp_value);
    }
}

/*********************************************************************
 * @fn      CheckHealthDataAlarm
 *
 * @brief   检测所有健康数据异常并立即发送报警信号
 *
 * @param   heart_rate - 心率值
 * @param   spo2 - 血氧值
 * @param   temp_value - 温度值（*10）
 *
 * @return  none
 */
void CheckHealthDataAlarm(u8 heart_rate, u8 spo2, u16 temp_value)
{
    u8 current_hr_alarm = 0;
    u8 current_temp_alarm = 0;
    u8 any_alarm = 0;  // 标记是否有任何异常
    static u8 last_any_alarm = 0;  // 记录上次是否有异常

    // 检测心率异常
    if(heart_rate > 0) {  // 只有有效心率才检测
        if(heart_rate > HR_HIGH_THRESHOLD) {
            current_hr_alarm = 1;  // 心率过高
            any_alarm = 1;
        } else if(heart_rate < HR_LOW_THRESHOLD) {
            current_hr_alarm = 2;  // 心率过低
            any_alarm = 1;
        }

        // 状态变化时发送报警，异常持续时也发送
        if(current_hr_alarm != last_hr_alarm_type) {
            if(current_hr_alarm > 0) {
                printf("检测到心率异常: %d次/分\r\n", heart_rate);
            } else {
                printf("心率恢复正常: %d次/分\r\n", heart_rate);
            }
            last_hr_alarm_type = current_hr_alarm;
        }

        // 如果当前异常，发送报警信号
        if(current_hr_alarm > 0) {
            SendHeartRateAlarmToBluetooth(heart_rate);
        }
    }

    // 检测温度异常
    if(temp_value > TEMP_HIGH_THRESHOLD || temp_value < TEMP_LOW_THRESHOLD) {
        current_temp_alarm = 1;
        any_alarm = 1;
    }

    // 血氧不设置报警信息与异常值，移除血氧异常检测

    // 检测温度异常（使用现有函数）
    CheckTemperatureAlarm(temp_value);

    // 统一异常报警：当有任何异常时发送统一报警信息
    if(any_alarm && !last_any_alarm) {
        // 发送统一的异常报警信息
        Bluetooth_Printf("病人编号001，身体情况出现异常反应，请立即前往4F012查看\r\n");
        printf("发送统一异常报警信息\r\n");
    } else if(!any_alarm && last_any_alarm) {
        // 异常恢复时发送恢复信息
        Bluetooth_Printf("病人编号001，身体情况已恢复正常\r\n");
        printf("发送异常恢复信息\r\n");
    }

    last_any_alarm = any_alarm;
}

/*********************************************************************
 * @fn      SendHeartRateAlarmToBluetooth
 *
 * @brief   向蓝牙模块发送心率异常报警信号
 *
 * @param   heart_rate - 当前心率值
 *
 * @return  none
 */
void SendHeartRateAlarmToBluetooth(u8 heart_rate)
{
    // 直接发送到蓝牙模块，不使用条件编译
    char alarm_msg[128];

    if(heart_rate > HR_HIGH_THRESHOLD) {
        sprintf(alarm_msg, "🔴🚨心率异常报警🚨🔴 当前心率:%d次/分 > 正常值:%d次/分 ❤️🔴🔴", heart_rate, HR_HIGH_THRESHOLD);
    } else {
        sprintf(alarm_msg, "🔴🚨心率异常报警🚨🔴 当前心率:%d次/分 < 正常值:%d次/分 💙🔴🔴", heart_rate, HR_LOW_THRESHOLD);
    }

    // 使用蓝牙专用发送函数
    Bluetooth_Printf("%s\r\n", alarm_msg);

    // 发送结构化报警数据（中文）
    Bluetooth_Printf("报警:心率异常:%s:%d次/分\r\n", (heart_rate > HR_HIGH_THRESHOLD) ? "过高" : "过低", heart_rate);
    Bluetooth_Printf("🔴警报🔴 🔴警报🔴 🔴警报🔴\r\n");

    // 同时发送到调试串口
    printf("蓝牙发送心率报警: %s\r\n", alarm_msg);
}

// 血氧不设置报警信息与异常值，已移除SendSpO2AlarmToBluetooth函数

/*********************************************************************
 * @fn      SendTemperatureAlarmToBluetooth
 *
 * @brief   向蓝牙模块发送醒目的温度报警信号
 *
 * @param   alarm_type - 报警类型：1-高温，2-低温
 * @param   temp_value - 当前温度值（*10）
 *
 * @return  none
 */
void SendTemperatureAlarmToBluetooth(u8 alarm_type, u16 temp_value)
{
    // 直接发送到蓝牙模块，不使用条件编译
    char alarm_msg[128];

    // 构建醒目的中文报警消息
    if(alarm_type == 1) {
        // 高温报警
        sprintf(alarm_msg, "🔴🚨体温异常报警🚨🔴 当前体温:%.1f°C > 正常值:40.0°C 🌡️🔴🔴", temp_value / 10.0f);
    } else if(alarm_type == 2) {
        // 低温报警
        sprintf(alarm_msg, "🔴🚨体温异常报警🚨🔴 当前体温:%.1f°C < 正常值:35.0°C ❄️🔴🔴", temp_value / 10.0f);
    }

    // 使用蓝牙专用发送函数
    Bluetooth_Printf("%s\r\n", alarm_msg);

    // 发送特殊的报警数据包（中文）
    Bluetooth_Printf("报警:体温异常:%s:%.1f°C\r\n",
            (alarm_type == 1) ? "过高" : "过低", temp_value / 10.0f);

    // 发送连续的红色警报信号（醒目效果）
    Bluetooth_Printf("🔴警报🔴 🔴警报🔴 🔴警报🔴\r\n");

    // 同时发送到调试串口
    printf("蓝牙发送体温报警: %s\r\n", alarm_msg);
}

/*********************************************************************
 * @fn      TestAlarmFunctions
 *
 * @brief   测试报警功能（用于调试）
 *
 * @return  none
 */
void TestAlarmFunctions(void)
{
    printf("开始测试报警功能...\r\n");

    // 测试心率过高报警
    printf("测试心率过高报警\r\n");
    SendHeartRateAlarmToBluetooth(130);

    Delay_Ms(1000);

    // 测试心率过低报警
    printf("测试心率过低报警\r\n");
    SendHeartRateAlarmToBluetooth(55);

    Delay_Ms(1000);

    // 血氧不设置报警信息与异常值，已移除血氧报警测试

    // 测试体温过高报警
    printf("测试体温过高报警\r\n");
    SendTemperatureAlarmToBluetooth(1, 415);  // 41.5°C

    Delay_Ms(1000);

    // 测试体温过低报警
    printf("测试体温过低报警\r\n");
    SendTemperatureAlarmToBluetooth(2, 340);  // 34.0°C

    printf("报警功能测试完成\r\n");
}

/*********************************************************************
 * @fn      ApplyNaturalVariation
 *
 * @brief   应用自然变化，产生更真实的非5倍数心率数据
 *
 * @param   base_hr - 基础心率值
 *
 * @return  调整后的心率值
 */
u8 ApplyNaturalVariation(u8 base_hr)
{
    static u8 variation_pattern[12] = {0, 1, 2, 1, 3, 1, 2, 2, 1, 3, 1, 2}; // 减小变化幅度的模式
    static u8 pattern_index = 0;

    // 使用预定义的变化模式，确保产生非5倍数
    int variation = variation_pattern[pattern_index];
    pattern_index = (pattern_index + 1) % 12;

    // 随机决定是加还是减
    if(GetRandomInRange(0, 100) < 50) {
        variation = -variation;
    }

    // 偶尔添加更小的非规律变化
    if(GetRandomInRange(0, 100) < 8) {  // 8%概率
        int extra_variation = GetRandomInRange(1, 4);  // 1-3的额外变化（减小幅度）
        if(extra_variation == 5) extra_variation = 3;  // 避免5的倍数
        variation += (GetRandomInRange(0, 100) < 50) ? extra_variation : -extra_variation;
    }

    int result = base_hr + variation;

    // 确保结果在合理范围内
    if(result < HR_MIN_VALUE) result = HR_MIN_VALUE;
    if(result > HR_MAX_VALUE) result = HR_MAX_VALUE;

    return (u8)result;
}

/*********************************************************************
 * @fn      UpdateHeartRateFactors
 *
 * @brief   更新影响心率的各种因子
 *
 * @return  none
 */
void UpdateHeartRateFactors(void)
{
    // 更新日常周期（模拟一天中的心率变化）
    daily_cycle_counter++;

    // 模拟一天中的心率变化（每1000次调用为一个周期）
    u16 cycle_phase = daily_cycle_counter % 1000;

    if(cycle_phase < 200) {
        // 早晨：心率逐渐上升（减小影响因子范围）
        activity_intensity = GetRandomInRange(1, 3);
        stress_level = GetRandomInRange(0, 2);
    } else if(cycle_phase < 400) {
        // 上午：活跃期
        activity_intensity = GetRandomInRange(2, 4);
        stress_level = GetRandomInRange(1, 3);
    } else if(cycle_phase < 600) {
        // 下午：高峰期
        activity_intensity = GetRandomInRange(3, 5);
        stress_level = GetRandomInRange(2, 4);
    } else if(cycle_phase < 800) {
        // 傍晚：逐渐放松
        activity_intensity = GetRandomInRange(2, 4);
        stress_level = GetRandomInRange(0, 2);
    } else {
        // 夜晚：休息期
        activity_intensity = GetRandomInRange(0, 2);
        stress_level = GetRandomInRange(0, 1);
        fatigue_level = GetRandomInRange(2, 5);
    }

    // 随机事件影响（减小影响幅度）
    if(GetRandomInRange(0, 100) < 3) {  // 3%概率发生随机事件
        stress_level = GetRandomInRange(4, 6);  // 适度压力增加
    }

    if(GetRandomInRange(0, 100) < 2) {  // 2%概率运动事件
        activity_intensity = GetRandomInRange(5, 7);  // 适度运动
    }
}

u8 SimulateHeartRate(void)
{
    static u8 change_counter = 0;
    static u32 mode_change_timer = 0;
    u8 mode_min, mode_max;

    // 更新影响因子
    UpdateHeartRateFactors();

    // 根据当前模式设置心率范围（限制最大值不超过105）
    switch(hr_simulation_mode) {
        case HR_MODE_SLEEP:
            mode_min = 53; mode_max = 67;  // 睡眠模式
            break;
        case HR_MODE_RESTING:
            mode_min = 58; mode_max = 77;  // 静息模式
            break;
        case HR_MODE_NORMAL:
            mode_min = 68; mode_max = 87;  // 正常模式
            break;
        case HR_MODE_ACTIVE:
            mode_min = 78; mode_max = 98;  // 活跃模式（降低上限）
            break;
        case HR_MODE_EXERCISE:
            mode_min = 88; mode_max = 105; // 运动模式（限制到105）
            break;
        default:
            mode_min = 68; mode_max = 87;  // 默认正常模式
            break;
    }

    // 定期切换模式（模拟不同活动状态）
    mode_change_timer++;
    if(mode_change_timer > 80) {  // 进一步缩短切换间隔，增加变化频率
        mode_change_timer = 0;
        hr_simulation_mode = GetRandomInRange(0, 4);  // 随机选择5种模式
        // 移除运动模式的蓝牙发送，仅保留本地调试
        // printf("HR Mode changed to: %s\r\n",
        //        hr_simulation_mode == 0 ? "SLEEP" :
        //        hr_simulation_mode == 1 ? "RESTING" :
        //        hr_simulation_mode == 2 ? "NORMAL" :
        //        hr_simulation_mode == 3 ? "ACTIVE" : "EXERCISE");
    }

    // 控制变化频率（提高变化频率）
    change_counter++;
    if(change_counter < 1) {  // 每次调用都可能变化
        return simulated_heart_rate;
    }
    change_counter = 0;

    // 根据概率决定是否变化（使用定义的变化概率）
    if(GetRandomInRange(0, 100) > HR_CHANGE_PROBABILITY) {  // 使用HR_CHANGE_PROBABILITY
        return simulated_heart_rate;
    }

    // 生成更复杂的变化模式（包含非5倍数的细致变化）
    static u8 trend_direction = 0;  // 0-随机，1-上升趋势，2-下降趋势
    static u8 trend_counter = 0;
    static u8 fine_adjustment_counter = 0;
    int change = 0;

    // 每10次调用改变一次趋势（加快趋势变化）
    if(trend_counter++ > 10) {
        trend_counter = 0;
        trend_direction = GetRandomInRange(0, 2);
    }

    // 根据趋势生成变化
    switch(trend_direction) {
        case 1: // 上升趋势
            change = GetRandomInRange(0, HR_MAX_CHANGE);  // 只正变化
            break;
        case 2: // 下降趋势
            change = -GetRandomInRange(0, HR_MAX_CHANGE); // 只负变化
            break;
        default: // 随机变化
            change = GetRandomInRange(0, HR_MAX_CHANGE * 2) - HR_MAX_CHANGE;
            break;
    }

    // 添加细微调整，产生非5倍数的数据（减小幅度）
    fine_adjustment_counter++;
    if(fine_adjustment_counter % 3 == 0) {
        // 每3次添加±1到±2的细微调整
        int fine_change = GetRandomInRange(0, 4) - 2;  // ±2的细微变化
        change += fine_change;
    }

    // 偶尔添加非规律的小幅调整
    if(GetRandomInRange(0, 100) < 15) {  // 15%概率
        int micro_change = GetRandomInRange(0, 2) - 1;  // ±1的微调
        change += micro_change;
    }

    // 偶尔添加突变（模拟情绪或活动突然变化，减小幅度）
    if(GetRandomInRange(0, 100) < 5) {  // 5%概率突变
        change = GetRandomInRange(0, 8) - 4;  // 小幅突变±4
    }

    int new_hr = simulated_heart_rate + change;

    // 应用影响因子（减小影响幅度）
    new_hr += (stress_level * 1);        // 压力增加心率（减小影响）
    new_hr += (activity_intensity * 2);  // 活动强度增加心率（减小影响）
    new_hr -= (fatigue_level * 1);       // 疲劳降低心率

    // 添加基于因子的细微调整，产生非5倍数数据
    int factor_adjustment = 0;
    factor_adjustment += (stress_level % 3);      // 基于压力的余数调整
    factor_adjustment += (activity_intensity % 4); // 基于活动的余数调整
    factor_adjustment -= (fatigue_level % 3);     // 基于疲劳的余数调整

    new_hr += factor_adjustment;

    // 限制在当前模式的范围内（小幅扩大范围以适应因子影响）
    int extended_min = mode_min - 5;
    int extended_max = mode_max + 5;
    if(new_hr < extended_min) new_hr = extended_min;
    if(new_hr > extended_max) new_hr = extended_max;

    // 如果超出总体范围，拉回到安全范围
    if(new_hr < HR_MIN_VALUE) new_hr = HR_MIN_VALUE;
    if(new_hr > HR_MAX_VALUE) new_hr = HR_MAX_VALUE;

    simulated_heart_rate = (u8)new_hr;

    // 应用自然变化，产生非5倍数的真实数据
    simulated_heart_rate = ApplyNaturalVariation(simulated_heart_rate);

    return simulated_heart_rate;
}

/*********************************************************************
 * @fn      GetProcessedHeartRate
 *
 * @brief   获取处理后的心率数据（真实或模拟）
 *
 * @param   raw_heart_rate - 原始心率数据
 *          is_valid - 数据是否有效
 *
 * @return  处理后的心率值
 */
u8 GetProcessedHeartRate(u8 raw_heart_rate, u8 is_valid)
{
    #if ENABLE_HR_SIMULATION
    // 使用模拟数据
    return SimulateHeartRate();
    #else
    // 使用真实传感器数据
    return SmoothHeartRateFilter(raw_heart_rate, is_valid, &last_valid_hr, &hr_invalid_count);
    #endif
}

/*********************************************************************
 * @fn      ClearDisplayArea
 *
 * @brief   清除指定显示区域（用于防闪烁）
 *
 * @param   x, y - 起始坐标
 *          width, height - 区域大小
 *          color - 背景颜色
 *
 * @return  none
 */
void ClearDisplayArea(u8 x, u8 y, u8 width, u8 height, u16 color)
{
    Z_ST7735S_SpecifyScope(x, x + width - 1, y, y + height - 1);
    for(u16 i = 0; i < width * height; i++) {
        Z_ST7735S_Send16bitsRGB(color);
    }
}

/*********************************************************************
 * @fn      ShowLargeNumber
 *
 * @brief   显示大号数字（增强可视性）
 *
 * @param   x, y - 显示位置
 *          number - 要显示的数字
 *          digits - 数字位数
 *          color - 颜色
 *          bg_color - 背景颜色
 *
 * @return  none
 */
void ShowLargeNumber(u8 x, u8 y, u16 number, u8 digits, u16 color, u16 bg_color)
{
    char str[6];
    u8 i;

    // 格式化数字字符串（两位数去除前导零）
    u8 str_index = 0;

    switch(digits) {
        case 2:
            // 对于两位数，如果十位为0则不显示
            if(number >= 10) {
                str[str_index++] = '0' + (number / 10) % 10;
            }
            // 个位数始终显示
            str[str_index++] = '0' + number % 10;
            str[str_index] = '\0';
            break;
        case 3:
            // 三位数保持原有逻辑（可能需要显示前导零以保持对齐）
            str[0] = '0' + (number / 100) % 10;
            str[1] = '0' + (number / 10) % 10;
            str[2] = '0' + number % 10;
            str[3] = '\0';
            break;
        default:
            str[0] = '0' + number % 10;
            str[1] = '\0';
            break;
    }

    // 清除背景（根据实际字符串长度计算）
    u8 actual_length = 0;
    while(str[actual_length] != '\0') actual_length++;  // 计算实际字符串长度
    ClearDisplayArea(x, y, digits * 8 + 4, 16, bg_color);  // 使用原始digits确保完全清除

    // 显示数字
    for(i = 0; str[i] != '\0'; i++) {
        Z_ST7735S_ShowChar(x + i * 8, y, str[i], color);
    }
}

/*********************************************************************
 * @fn      ShowTemperatureWithDecimal
 *
 * @brief   显示带小数点的温度值
 *
 * @param   x, y - 显示位置
 *          temp_value - 温度值（已乘以10）
 *          color - 颜色
 *          bg_color - 背景颜色
 *
 * @return  none
 */
void ShowTemperatureWithDecimal(u8 x, u8 y, u16 temp_value, u16 color, u16 bg_color)
{
    // 清除背景
    ClearDisplayArea(x, y, 40, 16, bg_color);

    // 显示温度值（去除前导零）
    u8 display_x = x;
    u8 hundreds = (temp_value / 100) % 10;
    u8 tens = (temp_value / 10) % 10;
    u8 ones = temp_value % 10;

    // 只有当百位不为0时才显示百位
    if(hundreds > 0) {
        Z_ST7735S_ShowChar(display_x, y, '0' + hundreds, color);
        display_x += 8;
    }

    // 只有当十位不为0或已经显示了百位时才显示十位
    if(tens > 0 || hundreds > 0) {
        Z_ST7735S_ShowChar(display_x, y, '0' + tens, color);
        display_x += 8;
    }

    // 显示小数点和个位数（始终显示）
    Z_ST7735S_ShowChar(display_x, y, '.', color);
    Z_ST7735S_ShowChar(display_x + 8, y, '0' + ones, color);
    Z_ST7735S_ShowString(display_x + 16, y, "°C", color);
}

/*********************************************************************
 * @fn      DrawPixel
 *
 * @brief   在指定位置绘制一个像素点
 *
 * @param   x - X坐标
 *          y - Y坐标
 *          color - 颜色值
 *
 * @return  none
 */
void DrawPixel(u8 x, u8 y, u16 color)
{
    if(x >= 128 || y >= 160) return;  // 边界检查
    Z_ST7735S_SpecifyScope(x, x, y, y);
    Z_ST7735S_Send16bitsRGB(color);
}

/*********************************************************************
 * @fn      DrawLine
 *
 * @brief   绘制直线
 *
 * @param   x1, y1 - 起始点坐标
 *          x2, y2 - 结束点坐标
 *          color - 颜色值
 *
 * @return  none
 */
void DrawLine(u8 x1, u8 y1, u8 x2, u8 y2, u16 color)
{
    int dx = x2 - x1;
    int dy = y2 - y1;
    int steps = (dx > dy) ? dx : dy;
    if(steps < 0) steps = -steps;

    if(steps == 0) {
        DrawPixel(x1, y1, color);
        return;
    }

    float x_inc = (float)dx / steps;
    float y_inc = (float)dy / steps;

    float x = x1;
    float y = y1;

    for(int i = 0; i <= steps; i++) {
        DrawPixel((u8)(x + 0.5), (u8)(y + 0.5), color);
        x += x_inc;
        y += y_inc;
    }
}

/*********************************************************************
 * @fn      DrawRectangle
 *
 * @brief   绘制矩形框
 *
 * @param   x, y - 左上角坐标
 *          width, height - 宽度和高度
 *          color - 颜色值
 *
 * @return  none
 */
void DrawRectangle(u8 x, u8 y, u8 width, u8 height, u16 color)
{
    // 绘制四条边
    DrawLine(x, y, x + width - 1, y, color);                    // 上边
    DrawLine(x, y + height - 1, x + width - 1, y + height - 1, color); // 下边
    DrawLine(x, y, x, y + height - 1, color);                   // 左边
    DrawLine(x + width - 1, y, x + width - 1, y + height - 1, color);  // 右边
}

/*********************************************************************
 * @fn      InitWaveformDisplay
 *
 * @brief   初始化波形显示区域
 *
 * @return  none
 */
void InitWaveformDisplay(void)
{
    // 使用优化的显示初始化
    InitOptimizedDisplay();

    // 简化界面，去除多余的波形标签

    // 绘制波形图边框
    DrawRectangle(WAVEFORM_X - 1, WAVEFORM_Y - 1, WAVEFORM_WIDTH + 2, WAVEFORM_HEIGHT + 2, COLOR_GRAY);

    // 清空波形区域
    Z_ST7735S_SpecifyScope(WAVEFORM_X, WAVEFORM_X + WAVEFORM_WIDTH - 1,
                          WAVEFORM_Y, WAVEFORM_Y + WAVEFORM_HEIGHT - 1);
    for(u16 i = 0; i < WAVEFORM_WIDTH * WAVEFORM_HEIGHT; i++) {
        Z_ST7735S_Send16bitsRGB(COLOR_BLACK);
    }

    // 绘制明显的中心基准线（增强对比）
    u8 center_y = WAVEFORM_Y + WAVEFORM_HEIGHT / 2;
    DrawLine(WAVEFORM_X, center_y, WAVEFORM_X + WAVEFORM_WIDTH - 1, center_y, COLOR_GRAY);

    // 添加上下参考线，增强波形对比
    u8 upper_y = WAVEFORM_Y + WAVEFORM_HEIGHT / 4;
    u8 lower_y = WAVEFORM_Y + (WAVEFORM_HEIGHT * 3) / 4;
    for(u8 i = 0; i < WAVEFORM_WIDTH; i += 20) {
        DrawPixel(WAVEFORM_X + i, upper_y, COLOR_DARK_GRAY);
        DrawPixel(WAVEFORM_X + i, lower_y, COLOR_DARK_GRAY);
    }

    // 初始化波形缓冲区
    for(u8 i = 0; i < WAVEFORM_WIDTH; i++) {
        hr_waveform_buffer[i] = WAVEFORM_HEIGHT / 2;  // 初始化为中心位置
    }
    waveform_index = 0;

    // 初始化时保持黑屏
    ClearScreen();
}

/*********************************************************************
 * @fn      UpdateHeartRateWaveform
 *
 * @brief   更新心率波形图
 *
 * @param   heart_rate - 当前心率值
 *
 * @return  none
 */
void UpdateHeartRateWaveform(u8 heart_rate)
{
    // 控制更新频率，每次调用都更新波形（最快响应）
    waveform_update_counter++;
    if(waveform_update_counter < 1) return;
    waveform_update_counter = 0;

    // 将心率值映射到波形高度范围，增加跳动感
    u8 mapped_value;
    static u8 last_heart_rate = 80;  // 记录上次心率值

    if(heart_rate == 0) {
        mapped_value = WAVEFORM_HEIGHT / 2;  // 无效值显示在中心
    } else {
        // 将心率范围40-150映射到波形高度，增加振幅
        if(heart_rate < 40) heart_rate = 40;
        if(heart_rate > 150) heart_rate = 150;

        // 生成真实的心跳波形，符合心率数据变化
        static u8 heartbeat_phase = 0;  // 心跳相位
        static u8 beats_per_cycle = 60;  // 每个周期的节拍数

        // 根据心率调整心跳周期（更敏感的响应）
        beats_per_cycle = 150 / (heart_rate > 0 ? heart_rate : 80);  // 增加敏感度
        if(beats_per_cycle < 15) beats_per_cycle = 15;  // 减小最小周期
        if(beats_per_cycle > 80) beats_per_cycle = 80;   // 减小最大周期

        heartbeat_phase = (heartbeat_phase + 1) % beats_per_cycle;

        // 生成心跳波形（模拟ECG波形）
        u8 base_level = WAVEFORM_HEIGHT / 2;  // 基准线
        int waveform_offset = 0;

        // 根据心率动态调整基准线位置，增强变化感
        base_level = base_level + ((heart_rate - 80) / 4);  // 心率高时基准线上移
        if(base_level < 10) base_level = 10;
        if(base_level > WAVEFORM_HEIGHT - 10) base_level = WAVEFORM_HEIGHT - 10;

        // 心跳波形的不同阶段（大幅增强振幅）
        if(heartbeat_phase < beats_per_cycle / 8) {
            // P波（明显上升）
            waveform_offset = (heartbeat_phase * 15) / beats_per_cycle;  // 增加振幅
        } else if(heartbeat_phase < beats_per_cycle / 4) {
            // QRS复合波（超大幅跳动）
            u8 qrs_phase = heartbeat_phase - beats_per_cycle / 8;
            if(qrs_phase < (beats_per_cycle / 8) / 2) {
                waveform_offset = -25 + (qrs_phase * 50) / (beats_per_cycle / 8);  // 大幅下降
            } else {
                waveform_offset = 25 - ((qrs_phase - (beats_per_cycle / 8) / 2) * 50) / (beats_per_cycle / 8);  // 大幅上升
            }
        } else if(heartbeat_phase < beats_per_cycle / 2) {
            // T波（明显上升）
            u8 t_phase = heartbeat_phase - beats_per_cycle / 4;
            waveform_offset = 12 - (t_phase * 24) / (beats_per_cycle / 4);  // 增加振幅
        } else {
            // 静息期（轻微波动）
            waveform_offset = (GetRandomInRange(0, 6) - 3);  // 添加轻微随机波动
        }

        // 根据心率大幅调整振幅，使变化更明显
        waveform_offset = (waveform_offset * heart_rate) / 60;  // 降低除数，增大振幅

        mapped_value = base_level + waveform_offset;
        if(mapped_value >= WAVEFORM_HEIGHT) mapped_value = WAVEFORM_HEIGHT - 1;
        if(mapped_value < 0) mapped_value = 0;

        last_heart_rate = heart_rate;  // 更新上次心率值
    }

    // 清除当前列
    u8 current_x = WAVEFORM_X + waveform_index;
    DrawLine(current_x, WAVEFORM_Y, current_x, WAVEFORM_Y + WAVEFORM_HEIGHT - 1, COLOR_BLACK);

    // 重新绘制中心线的当前列部分
    u8 center_y = WAVEFORM_Y + WAVEFORM_HEIGHT / 2;
    DrawPixel(current_x, center_y, COLOR_GRAY);

    // 绘制新的波形点
    u8 new_y = WAVEFORM_Y + mapped_value;

    // 如果不是第一个点，绘制连接线（超强视觉效果）
    if(waveform_index > 0) {
        u8 prev_x = current_x - 1;
        u8 prev_y = WAVEFORM_Y + hr_waveform_buffer[(waveform_index - 1 + WAVEFORM_WIDTH) % WAVEFORM_WIDTH];

        // 绘制主线条（亮红色）
        DrawLine(prev_x, prev_y, current_x, new_y, COLOR_BRIGHT_RED);

        // 大幅增加线条粗细，使波形更明显
        if(new_y > WAVEFORM_Y) {
            DrawPixel(current_x, new_y - 1, COLOR_BRIGHT_RED);
            if(new_y > WAVEFORM_Y + 1) DrawPixel(current_x, new_y - 2, COLOR_RED);
        }
        if(new_y < WAVEFORM_Y + WAVEFORM_HEIGHT - 1) {
            DrawPixel(current_x, new_y + 1, COLOR_BRIGHT_RED);
            if(new_y < WAVEFORM_Y + WAVEFORM_HEIGHT - 2) DrawPixel(current_x, new_y + 2, COLOR_RED);
        }

        // 在连接点增加超强调点
        DrawPixel(current_x, new_y, COLOR_BRIGHT_RED);

        // 增加前一个点的强调
        if(prev_y > WAVEFORM_Y) DrawPixel(prev_x, prev_y - 1, COLOR_BRIGHT_RED);
        if(prev_y < WAVEFORM_Y + WAVEFORM_HEIGHT - 1) DrawPixel(prev_x, prev_y + 1, COLOR_BRIGHT_RED);
    } else {
        // 绘制起始点（超强增强）
        DrawPixel(current_x, new_y, COLOR_BRIGHT_RED);
        if(new_y > WAVEFORM_Y) {
            DrawPixel(current_x, new_y - 1, COLOR_BRIGHT_RED);
            if(new_y > WAVEFORM_Y + 1) DrawPixel(current_x, new_y - 2, COLOR_RED);
        }
        if(new_y < WAVEFORM_Y + WAVEFORM_HEIGHT - 1) {
            DrawPixel(current_x, new_y + 1, COLOR_BRIGHT_RED);
            if(new_y < WAVEFORM_Y + WAVEFORM_HEIGHT - 2) DrawPixel(current_x, new_y + 2, COLOR_RED);
        }
    }

    // 更新缓冲区
    hr_waveform_buffer[waveform_index] = mapped_value;

    // 添加到蓝牙发送缓冲区
    waveform_send_buffer[waveform_send_counter] = mapped_value;
    waveform_send_counter++;

    // 发送波形数据到蓝牙（可通过宏开关控制）
    #if ENABLE_WAVEFORM_BLUETOOTH
        #if 1  // 使用紧凑格式（推荐）
        SendCompactWaveformData(heart_rate, mapped_value);
        #else  // 使用批量发送格式
        if(waveform_send_counter >= WAVEFORM_SEND_INTERVAL) {
            SendWaveformDataToBluetooth(waveform_send_buffer, WAVEFORM_SEND_INTERVAL, heart_rate);
            waveform_send_counter = 0;  // 重置发送计数器
        }
        #endif
    #endif

    // 更新索引
    waveform_index = (waveform_index + 1) % WAVEFORM_WIDTH;

    // 如果到达右边界，绘制下一个位置的清除线（预清除）
    if(waveform_index < WAVEFORM_WIDTH) {
        u8 next_x = WAVEFORM_X + waveform_index;
        DrawLine(next_x, WAVEFORM_Y, next_x, WAVEFORM_Y + WAVEFORM_HEIGHT - 1, COLOR_BLACK);
        DrawPixel(next_x, center_y, COLOR_GRAY);  // 重绘中心线
    }

    // 在波形图上方右侧显示当前心率数值（当前位置的对立面，确保与心率数据完全同步）
    static u8 last_displayed_hr = 255;  // 记录上次显示的心率

    // 确保显示的数据与传入的heart_rate参数完全一致，与主界面dis_hr同步
    if(heart_rate != last_displayed_hr) {
        // 计算显示位置：波形图上方右侧（当前左侧位置的对立面）
        u8 display_x = WAVEFORM_X + WAVEFORM_WIDTH - 32;  // 波形图右侧 (X=92)
        u8 display_y = WAVEFORM_Y - 12;                   // 波形图上方12像素处 (Y=48)

        // 清除之前的数值显示区域
        ClearDisplayArea(display_x, display_y, 32, 10, COLOR_BLACK);

        // 显示当前心率数值（去除前导零）
        u8 current_x = display_x;
        u8 hundreds = heart_rate / 100;
        u8 tens = (heart_rate / 10) % 10;
        u8 ones = heart_rate % 10;

        // 只有当百位不为0时才显示百位
        if(hundreds > 0) {
            Z_ST7735S_ShowChar(current_x, display_y, '0' + hundreds, COLOR_BRIGHT_BLUE);
            current_x += 8;
        }

        // 只有当十位不为0或已经显示了百位时才显示十位
        if(tens > 0 || hundreds > 0) {
            Z_ST7735S_ShowChar(current_x, display_y, '0' + tens, COLOR_BRIGHT_BLUE);
            current_x += 8;
        }

        // 个位数始终显示
        Z_ST7735S_ShowChar(current_x, display_y, '0' + ones, COLOR_BRIGHT_BLUE);
        current_x += 8;

        // 显示单位
        Z_ST7735S_ShowString(current_x, display_y, "bpm", COLOR_GRAY);

        last_displayed_hr = heart_rate;
    }
}

/*********************************************************************
 * @fn      SendWaveformDataToBluetooth
 *
 * @brief   发送波形数据到蓝牙模块
 *
 * @param   waveform_data - 波形数据数组
 *          data_count - 数据点数量
 *          heart_rate - 当前心率值
 *
 * @return  none
 */
void SendWaveformDataToBluetooth(u8 *waveform_data, u8 data_count, u8 heart_rate)
{
    // 通过蓝牙UART发送波形数据包
    Bluetooth_Printf("WAVEFORM_DATA:HR=%d,WAVE=", heart_rate);

    // 发送波形数据点
    for(u8 i = 0; i < data_count; i++) {
        Bluetooth_Printf("%d", waveform_data[i]);
        if(i < data_count - 1) Bluetooth_SendChar(',');
    }

    // 发送时间戳
    static u32 timestamp = 0;
    Bluetooth_Printf(",TIME=%d\r\n", timestamp++);
}

/*********************************************************************
 * @fn      SendCompactWaveformData
 *
 * @brief   发送紧凑格式的波形数据（优化传输效率）
 *          数据格式：W:心率值,波形值,时间戳
 *          示例：W:75,30,1234
 *          说明：心率75BPM，波形高度30（0-60范围），时间戳1234
 *
 * @param   heart_rate - 当前心率值
 *          waveform_value - 当前波形值（0-WAVEFORM_HEIGHT）
 *
 * @return  none
 */
void SendCompactWaveformData(u8 heart_rate, u8 waveform_value)
{
    // 通过蓝牙UART发送紧凑格式：W:心率值,波形值,时间戳
    static u16 compact_timestamp = 0;
    Bluetooth_Printf("W:%d,%d,%d\r\n", heart_rate, waveform_value, compact_timestamp++);
}

/*********************************************************************
 * @fn      Bluetooth_TestConnection
 *
 * @brief   测试蓝牙连接并发送测试数据
 *
 * @return  none
 */
void Bluetooth_TestConnection(void)
{
    static u8 test_counter = 0;

    // 发送测试信息
    Bluetooth_Printf("BT_TEST:Counter=%d,Status=OK\r\n", test_counter++);

    // 同时在调试串口显示
    printf("Bluetooth test sent: %d\r\n", test_counter - 1);
}

/*********************************************************************
 * @fn      Bluetooth_SendHealthData
 *
 * @brief   发送完整的健康数据（包含心率、血氧、体温）
 *
 * @param   heart_rate - 心率值
 *          spo2 - 血氧值
 *          temperature - 体温值（已乘以10）
 *
 * @return  none
 */
void Bluetooth_SendHealthData(u8 heart_rate, u8 spo2, u16 temperature)
{
    // 发送完整健康数据（中文）
    Bluetooth_Printf("健康数据:心率=%d次/分,血氧=%d%%,体温=%d.%d°C\r\n",
                    heart_rate, spo2,
                    temperature/10, temperature%10);
}

/*********************************************************************
 * @fn      UpdateHealthDataDisplay
 *
 * @brief   优化的健康数据显示更新（防闪烁）
 *
 * @param   heart_rate - 心率值
 *          spo2 - 血氧值
 *          temperature - 体温值
 *
 * @return  none
 */
void UpdateHealthDataDisplay(u8 heart_rate, u8 spo2, u16 temperature)
{
    // 新的显示逻辑：只有温度变化超过1度时才显示消息，平常保持黑屏
    // 这里不做任何显示操作，显示逻辑由DisplaySystemStatus统一管理

    display_refresh_flag = 0;  // 清除刷新标志
}

/*********************************************************************
 * @fn      InitOptimizedDisplay
 *
 * @brief   初始化优化的显示界面
 *
 * @return  none
 */
void InitOptimizedDisplay(void)
{
    // 当DS18B20工作正常时，显示实时温度值
    if(ds18b20_working) {
        DisplayTemperature();
    } else {
        // DS18B20未工作时，显示等待信息
        Z_ST7735S_RefreshAll(COLOR_BLACK);
        Z_ST7735S_ShowString(20, 60, "Waiting for", COLOR_YELLOW);
        Z_ST7735S_ShowString(20, 80, "DS18B20...", COLOR_YELLOW);
    }

    // 设置刷新标志
    display_refresh_flag = 1;
}

/*********************************************************************
 * @fn      ShowStatusIndicator
 *
 * @brief   显示状态指示器（传感器状态、蓝牙状态等）
 *
 * @param   x, y - 显示位置
 *          status - 状态（0-异常，1-正常）
 *          label - 标签
 *
 * @return  none
 */
void ShowStatusIndicator(u8 x, u8 y, u8 status, char *label)
{
    u16 color = status ? COLOR_BRIGHT_GREEN : COLOR_BRIGHT_RED;
    char status_char = status ? 'O' : 'X';

    Z_ST7735S_ShowString(x, y, label, COLOR_WHITE);
    Z_ST7735S_ShowChar(x + strlen(label) * 8, y, status_char, color);
}

/*********************************************************************
 * @fn      SetHeartRateSimulationParams
 *
 * @brief   设置心率模拟参数
 *
 * @param   base_hr - 基础心率值
 *          min_hr - 最小心率值
 *          max_hr - 最大心率值
 *          change_prob - 变化概率（0-100）
 *
 * @return  none
 */
void SetHeartRateSimulationParams(u8 base_hr, u8 min_hr, u8 max_hr, u8 change_prob)
{
    if(base_hr >= min_hr && base_hr <= max_hr) {
        simulated_heart_rate = base_hr;
    }

    // 这里可以添加动态参数调整的代码
    // 目前使用编译时常量，如需运行时调整可以改为变量
    printf("HR Simulation: Base=%d, Range=%d-%d, ChangeProb=%d%%\r\n",
           base_hr, min_hr, max_hr, change_prob);
}

/*********************************************************************
 * @fn      GetHeartRateSimulationStatus
 *
 * @brief   获取心率模拟状态信息
 *
 * @return  当前模拟心率值
 */
u8 GetHeartRateSimulationStatus(void)
{
    return simulated_heart_rate;
}

/*********************************************************************
 * @fn      ForceDisplayRefresh
 *
 * @brief   强制刷新显示（用于解决显示问题）
 *
 * @return  none
 */
void ForceDisplayRefresh(void)
{
    display_refresh_flag = 1;
    last_dis_hr = 255;
    last_dis_spo2 = 255;
    last_dis_temp = 0xFFFF;
}

/*********************************************************************
 * @fn      OptimizeDisplayBrightness
 *
 * @brief   优化显示亮度和对比度
 *
 * @param   brightness_level - 亮度级别（0-3）
 *
 * @return  none
 */
void OptimizeDisplayBrightness(u8 brightness_level)
{
    // 根据亮度级别调整显示参数
    // 这里可以添加LCD背光控制或颜色调整
    // 目前只是一个框架函数

    if(brightness_level > 3) brightness_level = 3;

    // 可以在这里添加具体的亮度控制代码
    // 例如：PWM控制背光、调整颜色饱和度等
}

/*********************************************************************
 * @fn      DisplaySystemStatus
 *
 * @brief   根据温度变化显示系统状态信息
 *
 * @param   none
 *
 * @return  none
 */
void DisplaySystemStatus(void)
{
    static u32 status_counter = 0;
    static u32 last_display_update = 0;
    static u8 first_call = 1;
    status_counter++;

    // 第一次调用时立即显示温度（即使DS18B20还未完全初始化）
    if(first_call) {
        printf("系统状态: 首次调用DisplaySystemStatus，立即显示温度\r\n");
        printf("DS18B20状态: working=%d, temperature=%.1f°C\r\n", ds18b20_working, ds18b20_temperature/10.0f);
        DisplayTemperature();
        first_call = 0;
        last_display_update = status_counter;
        return;
    }

    // 如果有温度数据（不管DS18B20状态如何），都显示温度值
    if(ds18b20_temperature > 0) {
        // 每50次循环更新一次显示（更频繁的更新）
        if(status_counter - last_display_update >= 50) {
            DisplayTemperature();
            last_display_update = status_counter;
            if(status_counter % 500 == 0) {  // 每500次输出一次调试信息
                printf("系统状态: 显示温度 %.1f°C (working=%d, 计数:%d)\r\n",
                       ds18b20_temperature/10.0f, ds18b20_working, status_counter);
            }
        }
    } else {
        // 完全没有温度数据时，显示等待状态
        static u8 waiting_screen_set = 0;
        if(!waiting_screen_set || status_counter % 500 == 0) {
            Z_ST7735S_RefreshAll(COLOR_BLACK);
            Z_ST7735S_ShowString(15, 50, "Initializing", COLOR_YELLOW);
            Z_ST7735S_ShowString(20, 70, "DS18B20...", COLOR_YELLOW);
            Z_ST7735S_ShowString(10, 90, "Please wait", COLOR_CYAN);
            waiting_screen_set = 1;
            printf("系统状态: 初始化DS18B20传感器 (计数:%d)\r\n", status_counter);
        }
    }
}

/*********************************************************************
 * @fn      TestDS18B20Status
 *
 * @brief   测试DS18B20状态显示功能
 *
 * @param   none
 *
 * @return  none
 */
void TestDS18B20Status(void)
{
    printf("=== DS18B20状态测试 ===\r\n");
    printf("DS18B20初始化状态: %s\r\n", ds18b20_initialized ? "成功" : "失败");
    printf("DS18B20工作状态: %s\r\n", ds18b20_working ? "正常" : "等待");
    printf("当前温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
    printf("状态显示标志: %s\r\n", system_status_displayed ? "已显示" : "未显示");
    printf("========================\r\n");
}

/*********************************************************************
 * @fn      TestTemperatureDisplay
 *
 * @brief   测试温度显示功能
 *
 * @param   none
 *
 * @return  none
 */
void TestTemperatureDisplay(void)
{
    printf("=== 温度显示测试 ===\r\n");
    printf("开始测试温度显示功能...\r\n");

    // 强制显示温度值进行测试
    DisplayTemperature();

    printf("温度值已显示在OLED屏幕上\r\n");
    printf("显示内容: 温度 %.1f°C\r\n", ds18b20_temperature / 10.0f);
    printf("==================\r\n");
}

/*********************************************************************
 * @fn      TestChineseDisplay
 *
 * @brief   测试中文显示功能（保留备用）
 *
 * @param   none
 *
 * @return  none
 */
void TestChineseDisplay(void)
{
    printf("=== 中文显示测试 ===\r\n");
    printf("开始测试中文字体显示...\r\n");

    // 强制显示中文消息进行测试
    DisplayChineseMessage();

    printf("中文消息已显示在OLED屏幕上\r\n");
    printf("显示内容: 已启动，硬件功能一切正常\r\n");
    printf("==================\r\n");
}

/*********************************************************************
 * @fn      TestTemperatureChange
 *
 * @brief   测试温度变化检测功能
 *
 * @param   none
 *
 * @return  none
 */
void TestTemperatureChange(void)
{
    printf("=== 温度变化检测测试 ===\r\n");
    printf("当前温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
    printf("基准温度: %.1f°C\r\n", last_stable_temperature / 10.0f);
    printf("温度变化检测标志: %s\r\n", temperature_change_detected ? "已检测到" : "未检测到");
    printf("消息显示状态: %s\r\n", message_displayed ? "正在显示" : "未显示");
    printf("消息显示计时器: %d\r\n", message_display_timer);
    printf("DS18B20工作状态: %s\r\n", ds18b20_working ? "正常工作" : "未工作");
    printf("强制触发标志: %s\r\n", force_display_trigger ? "已设置" : "未设置");
    printf("========================\r\n");
}

/*********************************************************************
 * @fn      ForceDisplayTest
 *
 * @brief   强制触发温度显示测试
 *
 * @param   none
 *
 * @return  none
 */
void ForceDisplayTest(void)
{
    printf("=== 强制温度显示测试 ===\r\n");
    printf("手动触发温度显示...\r\n");
    force_display_trigger = 1;
    temperature_change_detected = 1;
    printf("触发标志已设置，等待下次DisplaySystemStatus调用\r\n");
    printf("当前温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
    printf("========================\r\n");
}

void Z_SPI_SetSS(uint8_t val){
    if(val == 0){
        GPIO_WriteBit(GPIOA, ST7735_SS_GPIO, Bit_RESET);
    }else {
        GPIO_WriteBit(GPIOA, ST7735_SS_GPIO, Bit_SET);
    }
}

void Z_SPI_SetSCK(uint8_t val){
    if(val == 0){
        GPIO_WriteBit(GPIOA, ST7735_SCK_GPIO, Bit_RESET);
    }else {
        GPIO_WriteBit(GPIOA, ST7735_SCK_GPIO, Bit_SET);
    }
}

void Z_SPI_SetMOSI(uint8_t val){
    if(val == 0){
        GPIO_WriteBit(GPIOA, ST7735_MOSI_GPIO, Bit_RESET);
    }else {
        GPIO_WriteBit(GPIOA, ST7735_MOSI_GPIO, Bit_SET);
    }
}

uint8_t Z_SPI_GetMISO(void){
    return GPIO_ReadInputDataBit(GPIOA, ST7735_MISO_GPIO);
}

/* GPIO��ʼ�� */
void ST7735_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    GPIO_InitStructure.GPIO_Pin   = ST7735_SS_GPIO | ST7735_SCK_GPIO | ST7735_MOSI_GPIO;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    GPIO_InitStructure.GPIO_Pin   = ST7735_MISO_GPIO;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_IPU;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    //SSĬ���ǲ�ѡ��,���ߵ�ƽ
    Z_SPI_SetSS(1);
    //SCKĬ�ϵ͵�ƽ,�Ա�ƴ�Ӻ��������ֽڵ�ʱ��
    Z_SPI_SetSCK(0);
}

//SPI��ʼʱ��
void Z_SPI_Start(void){
    Z_SPI_SetSS(0);
}

//SPI����ʱ��
void Z_SPI_End(void){
    Z_SPI_SetSS(1);
}

//�����ֽ�ʱ��
uint8_t Z_SPI_SwapByte(uint8_t data){
    uint8_t receive=0x00;
    for(uint8_t i=0;i<8;++i){
        Z_SPI_SetMOSI(data&(0x80>>i));
        Z_SPI_SetSCK(1);
        if(Z_SPI_GetMISO()==1) receive|=(0x80>>i);
        Z_SPI_SetSCK(0);
    }
    return receive;
}

void Z_ST7735S_SetRST(uint8_t val){
    if(val==0) GPIO_WriteBit(GPIOA,ST7735S_RST_GPIO,Bit_RESET);
    else GPIO_WriteBit(GPIOA,ST7735S_RST_GPIO,Bit_SET);
}

void Z_ST7735S_SetDC(uint8_t val){
    if(val==0) GPIO_WriteBit(GPIOA,ST7735S_DC_GPIO,Bit_RESET);
    else GPIO_WriteBit(GPIOA,ST7735S_DC_GPIO,Bit_SET);
}

void Z_ST7735S_SendCommand(uint8_t command){
    Z_SPI_Start();
    Z_ST7735S_SetDC(0);
    Z_SPI_SwapByte(command);
    Z_SPI_End();
}

void Z_ST7735S_SendData(uint8_t data){
    Z_SPI_Start();
    Z_ST7735S_SetDC(1);
    Z_SPI_SwapByte(data);
    Z_SPI_End();
}

void Z_ST7735S_Send16bitsRGB(uint16_t rgb){
    Z_ST7735S_SendData(rgb>>8);
    Z_ST7735S_SendData(rgb);
}


/* LCD��ʼ�� */
void ST7735_Init(void)
{
    ST7735_GPIO_Init();
    GPIO_InitTypeDef GPIO_InitStructure = {0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    GPIO_InitStructure.GPIO_Pin   = ST7735S_DC_GPIO | ST7735S_RST_GPIO;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    Z_ST7735S_SetRST(0);
    Delay_Ms(1);
    Z_ST7735S_SetRST(1);
    Delay_Ms(120);
    //�����ṩ�Ĺ̶��ĳ�ʼ������
    Z_ST7735S_SendCommand(0x11); //Sleep out
    Delay_Ms(120);              //Delay 120ms
    //------------------------------------ST7735S Frame Rate-----------------------------------------//
    Z_ST7735S_SendCommand(0xB1);
    Z_ST7735S_SendData(0x05);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendCommand(0xB2);
    Z_ST7735S_SendData(0x05);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendCommand(0xB3);
    Z_ST7735S_SendData(0x05);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendData(0x05);
    Z_ST7735S_SendData(0x3C);
    Z_ST7735S_SendData(0x3C);
    //------------------------------------End ST7735S Frame Rate---------------------------------//
    Z_ST7735S_SendCommand(0xB4); //Dot inversion
    Z_ST7735S_SendData(0x03);
    //------------------------------------ST7735S Power Sequence---------------------------------//
    Z_ST7735S_SendCommand(0xC0);
    Z_ST7735S_SendData(0x28);
    Z_ST7735S_SendData(0x08);
    Z_ST7735S_SendData(0x04);
    Z_ST7735S_SendCommand(0xC1);
    Z_ST7735S_SendData(0XC0);
    Z_ST7735S_SendCommand(0xC2);
    Z_ST7735S_SendData(0x0D);
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendCommand(0xC3);
    Z_ST7735S_SendData(0x8D);
    Z_ST7735S_SendData(0x2A);
    Z_ST7735S_SendCommand(0xC4);
    Z_ST7735S_SendData(0x8D);
    Z_ST7735S_SendData(0xEE);
    //---------------------------------End ST7735S Power Sequence-------------------------------------//
    Z_ST7735S_SendCommand(0xC5); //VCOM
    Z_ST7735S_SendData(0x1A);
    Z_ST7735S_SendCommand(0x36); //MX, MY, RGB mode
    Z_ST7735S_SendData(0xC0);
    //------------------------------------ST7735S Gamma Sequence---------------------------------//
    Z_ST7735S_SendCommand(0xE0);
    Z_ST7735S_SendData(0x04);
    Z_ST7735S_SendData(0x22);
    Z_ST7735S_SendData(0x07);
    Z_ST7735S_SendData(0x0A);
    Z_ST7735S_SendData(0x2E);
    Z_ST7735S_SendData(0x30);
    Z_ST7735S_SendData(0x25);
    Z_ST7735S_SendData(0x2A);
    Z_ST7735S_SendData(0x28);
    Z_ST7735S_SendData(0x26);
    Z_ST7735S_SendData(0x2E);
    Z_ST7735S_SendData(0x3A);
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(0x01);
    Z_ST7735S_SendData(0x03);
    Z_ST7735S_SendData(0x13);
    Z_ST7735S_SendCommand(0xE1);
    Z_ST7735S_SendData(0x04);
    Z_ST7735S_SendData(0x16);
    Z_ST7735S_SendData(0x06);
    Z_ST7735S_SendData(0x0D);
    Z_ST7735S_SendData(0x2D);
    Z_ST7735S_SendData(0x26);
    Z_ST7735S_SendData(0x23);
    Z_ST7735S_SendData(0x27);
    Z_ST7735S_SendData(0x27);
    Z_ST7735S_SendData(0x25);
    Z_ST7735S_SendData(0x2D);
    Z_ST7735S_SendData(0x3B);
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(0x01);
    Z_ST7735S_SendData(0x04);
    Z_ST7735S_SendData(0x13);
    //------------------------------------End ST7735S Gamma Sequence-----------------------------//
    Z_ST7735S_SendCommand(0x3A); //65k mode
    Z_ST7735S_SendData(0x05);
    Z_ST7735S_SendCommand(0x29); //Display on
}

//ָ����Χ
void Z_ST7735S_SpecifyScope(uint8_t xs,uint8_t xe,uint8_t ys,uint8_t ye){
    Z_ST7735S_SendCommand(0x2A);    //ָ���з�Χ
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(xs);
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(xe);

    Z_ST7735S_SendCommand(0x2B);    //ָ���з�Χ
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(ys);
    Z_ST7735S_SendData(0x00);
    Z_ST7735S_SendData(ye);

    Z_ST7735S_SendCommand(0x2C);    //��ʼ�ڴ�д��
}


void Z_ST7735S_RefreshAll(uint16_t rgb){
    Z_ST7735S_SpecifyScope(0,128,0,160);
    for(uint16_t j=0;j<160;++j){
        for(uint16_t i=0;i<128;++i){
            Z_ST7735S_Send16bitsRGB(rgb);
        }
    }
}
const unsigned char asc2_1608[95][16]={
        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*" ",0*/

        {0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x10,0x10,0x00,0x00},/*"!",1*/

        {0x00,0x12,0x24,0x24,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*""",2*/

        {0x00,0x00,0x00,0x12,0x12,0x12,0x7E,0x24,0x24,0x24,0x7E,0x24,0x24,0x24,0x00,0x00},/*"#",3*/

        {0x00,0x00,0x00,0x08,0x3C,0x4A,0x4A,0x48,0x38,0x0A,0x0A,0x4A,0x4A,0x3C,0x08,0x08},/*"$",4*/

        {0x00,0x00,0x00,0x44,0xA4,0xA8,0xA8,0xB0,0x54,0x1A,0x2A,0x2A,0x4A,0x44,0x00,0x00},/*"%",5*/

        {0x00,0x00,0x00,0x30,0x48,0x48,0x48,0x50,0x6E,0xA4,0x94,0x98,0x89,0x76,0x00,0x00},/*"&",6*/

        {0x00,0x60,0x20,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"'",7*/

        {0x00,0x02,0x04,0x08,0x08,0x10,0x10,0x10,0x10,0x10,0x10,0x08,0x08,0x04,0x02,0x00},/*"(",8*/

        {0x00,0x40,0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x40,0x00},/*")",9*/

        {0x00,0x00,0x00,0x00,0x10,0x10,0xD6,0x38,0x38,0xD6,0x10,0x10,0x00,0x00,0x00,0x00},/*"*",10*/

        {0x00,0x00,0x00,0x00,0x00,0x08,0x08,0x08,0x7F,0x08,0x08,0x08,0x00,0x00,0x00,0x00},/*"+",11*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x20,0x20,0x40},/*",",12*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"-",13*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00},/*".",14*/

        {0x00,0x00,0x02,0x04,0x04,0x04,0x08,0x08,0x10,0x10,0x10,0x20,0x20,0x40,0x40,0x00},/*"/",15*/

        {0x00,0x00,0x00,0x18,0x24,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x24,0x18,0x00,0x00},/*"0",16*/

        {0x00,0x00,0x00,0x08,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x3E,0x00,0x00},/*"1",17*/

        {0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x02,0x04,0x08,0x10,0x20,0x42,0x7E,0x00,0x00},/*"2",18*/

        {0x00,0x00,0x00,0x3C,0x42,0x42,0x02,0x04,0x18,0x04,0x02,0x42,0x42,0x3C,0x00,0x00},/*"3",19*/

        {0x00,0x00,0x00,0x04,0x0C,0x0C,0x14,0x24,0x24,0x44,0x7F,0x04,0x04,0x1F,0x00,0x00},/*"4",20*/

        {0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x78,0x44,0x02,0x02,0x42,0x44,0x38,0x00,0x00},/*"5",21*/

        {0x00,0x00,0x00,0x18,0x24,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x22,0x1C,0x00,0x00},/*"6",22*/

        {0x00,0x00,0x00,0x7E,0x42,0x04,0x04,0x08,0x08,0x10,0x10,0x10,0x10,0x10,0x00,0x00},/*"7",23*/

        {0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x24,0x18,0x24,0x42,0x42,0x42,0x3C,0x00,0x00},/*"8",24*/

        {0x00,0x00,0x00,0x38,0x44,0x42,0x42,0x42,0x46,0x3A,0x02,0x02,0x24,0x18,0x00,0x00},/*"9",25*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00},/*":",26*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10},/*";",27*/

        {0x00,0x00,0x00,0x02,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x02,0x00,0x00},/*"<",28*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00},/*"=",29*/

        {0x00,0x00,0x00,0x40,0x20,0x10,0x08,0x04,0x02,0x04,0x08,0x10,0x20,0x40,0x00,0x00},/*">",30*/

        {0x00,0x00,0x00,0x3C,0x42,0x42,0x62,0x04,0x08,0x08,0x08,0x00,0x18,0x18,0x00,0x00},/*"?",31*/

        {0x00,0x00,0x00,0x38,0x44,0x5A,0xAA,0xAA,0xAA,0xAA,0xAA,0x5C,0x42,0x3C,0x00,0x00},/*"@",32*/

        {0x00,0x00,0x00,0x10,0x10,0x18,0x28,0x28,0x24,0x3C,0x44,0x42,0x42,0xE7,0x00,0x00},/*"A",33*/

        {0x00,0x00,0x00,0xF8,0x44,0x44,0x44,0x78,0x44,0x42,0x42,0x42,0x44,0xF8,0x00,0x00},/*"B",34*/

        {0x00,0x00,0x00,0x3E,0x42,0x42,0x80,0x80,0x80,0x80,0x80,0x42,0x44,0x38,0x00,0x00},/*"C",35*/

        {0x00,0x00,0x00,0xF8,0x44,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x44,0xF8,0x00,0x00},/*"D",36*/

        {0x00,0x00,0x00,0xFC,0x42,0x48,0x48,0x78,0x48,0x48,0x40,0x42,0x42,0xFC,0x00,0x00},/*"E",37*/

        {0x00,0x00,0x00,0xFC,0x42,0x48,0x48,0x78,0x48,0x48,0x40,0x40,0x40,0xE0,0x00,0x00},/*"F",38*/

        {0x00,0x00,0x00,0x3C,0x44,0x44,0x80,0x80,0x80,0x8E,0x84,0x44,0x44,0x38,0x00,0x00},/*"G",39*/

        {0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x7E,0x42,0x42,0x42,0x42,0xE7,0x00,0x00},/*"H",40*/

        {0x00,0x00,0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00},/*"I",41*/

        {0x00,0x00,0x00,0x3E,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x88,0xF0},/*"J",42*/

        {0x00,0x00,0x00,0xEE,0x44,0x48,0x50,0x70,0x50,0x48,0x48,0x44,0x44,0xEE,0x00,0x00},/*"K",43*/

        {0x00,0x00,0x00,0xE0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x42,0xFE,0x00,0x00},/*"L",44*/

        {0x00,0x00,0x00,0xEE,0x6C,0x6C,0x6C,0x6C,0x6C,0x54,0x54,0x54,0x54,0xD6,0x00,0x00},/*"M",45*/

        {0x00,0x00,0x00,0xC7,0x62,0x62,0x52,0x52,0x4A,0x4A,0x4A,0x46,0x46,0xE2,0x00,0x00},/*"N",46*/

        {0x00,0x00,0x00,0x38,0x44,0x82,0x82,0x82,0x82,0x82,0x82,0x82,0x44,0x38,0x00,0x00},/*"O",47*/

        {0x00,0x00,0x00,0xFC,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,0x40,0x40,0xE0,0x00,0x00},/*"P",48*/

        {0x00,0x00,0x00,0x38,0x44,0x82,0x82,0x82,0x82,0x82,0x82,0xB2,0x4C,0x38,0x06,0x00},/*"Q",49*/

        {0x00,0x00,0x00,0xFC,0x42,0x42,0x42,0x7C,0x48,0x48,0x44,0x44,0x42,0xE3,0x00,0x00},/*"R",50*/

        {0x00,0x00,0x00,0x3E,0x42,0x42,0x40,0x20,0x18,0x04,0x02,0x42,0x42,0x7C,0x00,0x00},/*"S",51*/

        {0x00,0x00,0x00,0xFE,0x92,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00},/*"T",52*/

        {0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00},/*"U",53*/

        {0x00,0x00,0x00,0xE7,0x42,0x42,0x44,0x24,0x24,0x28,0x28,0x18,0x10,0x10,0x00,0x00},/*"V",54*/

        {0x00,0x00,0x00,0xD6,0x54,0x54,0x54,0x54,0x54,0x6C,0x28,0x28,0x28,0x28,0x00,0x00},/*"W",55*/

        {0x00,0x00,0x00,0xE7,0x42,0x24,0x24,0x18,0x18,0x18,0x24,0x24,0x42,0xE7,0x00,0x00},/*"X",56*/

        {0x00,0x00,0x00,0xEE,0x44,0x44,0x28,0x28,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00},/*"Y",57*/

        {0x00,0x00,0x00,0x7E,0x84,0x04,0x08,0x08,0x10,0x20,0x20,0x42,0x42,0xFC,0x00,0x00},/*"Z",58*/

        {0x00,0x1E,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x1E,0x00},/*"[",59*/

        {0x00,0x00,0x40,0x20,0x20,0x20,0x10,0x10,0x10,0x08,0x08,0x04,0x04,0x04,0x02,0x02},/*"\",60*/

        {0x00,0x78,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x78,0x00},/*"]",61*/

        {0x00,0x18,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"^",62*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF},/*"_",63*/

        {0x00,0x60,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"`",64*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x0C,0x34,0x44,0x4C,0x36,0x00,0x00},/*"a",65*/

        {0x00,0x00,0x00,0x00,0xC0,0x40,0x40,0x58,0x64,0x42,0x42,0x42,0x64,0x58,0x00,0x00},/*"b",66*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x22,0x40,0x40,0x40,0x22,0x1C,0x00,0x00},/*"c",67*/

        {0x00,0x00,0x00,0x00,0x06,0x02,0x02,0x3E,0x42,0x42,0x42,0x42,0x46,0x3B,0x00,0x00},/*"d",68*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x7E,0x40,0x42,0x3C,0x00,0x00},/*"e",69*/

        {0x00,0x00,0x00,0x00,0x0C,0x12,0x10,0x7C,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00},/*"f",70*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x44,0x44,0x38,0x40,0x3C,0x42,0x42,0x3C},/*"g",71*/

        {0x00,0x00,0x00,0x00,0xC0,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0xE7,0x00,0x00},/*"h",72*/

        {0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00},/*"i",73*/

        {0x00,0x00,0x00,0x0C,0x0C,0x00,0x00,0x1C,0x04,0x04,0x04,0x04,0x04,0x04,0x44,0x78},/*"j",74*/

        {0x00,0x00,0x00,0x00,0xC0,0x40,0x40,0x4E,0x48,0x50,0x70,0x48,0x44,0xEE,0x00,0x00},/*"k",75*/

        {0x00,0x00,0x00,0x10,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00},/*"l",76*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x49,0x49,0x49,0x49,0x49,0xED,0x00,0x00},/*"m",77*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x62,0x42,0x42,0x42,0x42,0xE7,0x00,0x00},/*"n",78*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00},/*"o",79*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD8,0x64,0x42,0x42,0x42,0x64,0x58,0x40,0xE0},/*"p",80*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1A,0x26,0x42,0x42,0x42,0x26,0x1A,0x02,0x07},/*"q",81*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xEE,0x32,0x20,0x20,0x20,0x20,0xF8,0x00,0x00},/*"r",82*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x40,0x3C,0x02,0x42,0x7C,0x00,0x00},/*"s",83*/

        {0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x10,0x10,0x12,0x0C,0x00,0x00},/*"t",84*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x42,0x42,0x42,0x42,0x46,0x3B,0x00,0x00},/*"u",85*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xEE,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00},/*"v",86*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x89,0x4A,0x5A,0x54,0x24,0x24,0x00,0x00},/*"w",87*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x76,0x24,0x18,0x18,0x18,0x24,0x6E,0x00,0x00},/*"x",88*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x42,0x24,0x24,0x18,0x18,0x10,0x10,0x60},/*"y",89*/

        {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x44,0x08,0x10,0x10,0x22,0x7E,0x00,0x00},/*"z",90*/

        {0x00,0x03,0x04,0x04,0x04,0x04,0x04,0x04,0x08,0x04,0x04,0x04,0x04,0x04,0x03,0x00},/*"{",91*/

        {0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08},/*"|",92*/

        {0x00,0xC0,0x20,0x20,0x20,0x20,0x20,0x20,0x10,0x20,0x20,0x20,0x20,0x20,0xC0,0x00},/*"}",93*/

        {0x20,0x5A,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"~",94*/
};

// 中文字体数据 16x16像素
// "已" 字体数据
const unsigned char chinese_yi[32] = {
    0x00,0x00,0x00,0x00,0x3F,0xF8,0x00,0x08,0x00,0x08,0x00,0x08,0x3F,0xF8,0x00,0x08,
    0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00
};

// "启" 字体数据
const unsigned char chinese_qi[32] = {
    0x00,0x00,0x00,0x00,0x1F,0xF0,0x10,0x10,0x10,0x10,0x1F,0xF0,0x10,0x10,0x10,0x10,
    0x1F,0xF0,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x1F,0xF0,0x00,0x00,0x00,0x00
};

// "动" 字体数据
const unsigned char chinese_dong[32] = {
    0x00,0x00,0x00,0x00,0x08,0x20,0x08,0x20,0x08,0x20,0x0F,0xE0,0x08,0x20,0x08,0x20,
    0x08,0x20,0x0F,0xE0,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x00,0x00,0x00,0x00
};

// "硬" 字体数据
const unsigned char chinese_ying[32] = {
    0x00,0x00,0x00,0x00,0x1F,0xF8,0x10,0x08,0x10,0x08,0x1F,0xF8,0x10,0x08,0x10,0x08,
    0x1F,0xF8,0x10,0x08,0x10,0x08,0x10,0x08,0x10,0x08,0x1F,0xF8,0x00,0x00,0x00,0x00
};

// "件" 字体数据
const unsigned char chinese_jian[32] = {
    0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x3F,0xF8,0x08,0x00,0x08,0x00,
    0x08,0x00,0x0F,0xE0,0x08,0x20,0x08,0x20,0x08,0x20,0x0F,0xE0,0x00,0x00,0x00,0x00
};

// "功" 字体数据
const unsigned char chinese_gong[32] = {
    0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x3F,0xF8,0x08,0x00,
    0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00
};

// "能" 字体数据
const unsigned char chinese_neng[32] = {
    0x00,0x00,0x00,0x00,0x1F,0xF0,0x10,0x10,0x10,0x10,0x1F,0xF0,0x10,0x10,0x10,0x10,
    0x1F,0xF0,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x1F,0xF0,0x00,0x00,0x00,0x00
};

// "一" 字体数据
const unsigned char chinese_yi2[32] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
};

// "切" 字体数据
const unsigned char chinese_qie[32] = {
    0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x3F,0xF8,0x08,0x00,0x08,0x00,
    0x08,0x00,0x08,0x00,0x08,0x00,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
};

// "正" 字体数据
const unsigned char chinese_zheng[32] = {
    0x00,0x00,0x00,0x00,0x3F,0xF8,0x00,0x08,0x00,0x08,0x00,0x08,0x3F,0xF8,0x00,0x08,
    0x00,0x08,0x00,0x08,0x00,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
};

// "常" 字体数据
const unsigned char chinese_chang[32] = {
    0x00,0x00,0x00,0x00,0x3F,0xF8,0x20,0x08,0x20,0x08,0x3F,0xF8,0x20,0x08,0x20,0x08,
    0x3F,0xF8,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00
};

void Z_ST7735S_ShowChar(uint8_t x,uint8_t y,uint8_t ch,uint16_t rgb){
    if(ch>=95) return ;
    ch = ch - ' ';
    Z_ST7735S_SpecifyScope(x,x+7,y,y+15);
    for(uint8_t i=0;i<16;++i){
        uint8_t temp=asc2_1608[ch][i];
        for(uint8_t j=0;j<8;++j){
            if((temp&0x80)!=0) Z_ST7735S_Send16bitsRGB(rgb);
            else Z_ST7735S_Send16bitsRGB(COLOR_BLACK);  // 使用黑色背景
            temp<<=1;
        }
    }
}

void Z_ST7735S_ShowString(u8 x,u8 y,u8 *chr,uint16_t rgb)
{
    while((*chr>=' ')&&(*chr<='~'))
    {
        Z_ST7735S_ShowChar(x,y,*chr,rgb);
        x+=8;
        chr++;
  }
}

/*********************************************************************
 * @fn      Z_ST7735S_ShowChinese
 *
 * @brief   显示16x16中文字符
 *
 * @param   x,y - 显示位置
 *          font_data - 中文字体数据指针
 *          rgb - 颜色
 *
 * @return  none
 */
void Z_ST7735S_ShowChinese(uint8_t x, uint8_t y, const unsigned char *font_data, uint16_t rgb)
{
    Z_ST7735S_SpecifyScope(x, x+15, y, y+15);  // 16x16像素
    for(uint8_t i = 0; i < 32; i++) {  // 32字节数据
        uint8_t temp = font_data[i];
        for(uint8_t j = 0; j < 8; j++) {
            if((temp & 0x80) != 0) {
                Z_ST7735S_Send16bitsRGB(rgb);
            } else {
                Z_ST7735S_Send16bitsRGB(COLOR_BLACK);
            }
            temp <<= 1;
        }
    }
}

/*********************************************************************
 * @fn      CheckTemperatureChange
 *
 * @brief   检测温度变化（任何变化都触发显示）
 *
 * @param   current_temp - 当前温度值
 *
 * @return  1 - 检测到温度变化, 0 - 温度无变化
 */
u8 CheckTemperatureChange(float current_temp)
{
    static u32 temp_check_counter = 0;  // 温度检测计数器
    temp_check_counter++;

    // 如果是第一次检测，记录初始温度并触发显示
    if(last_stable_temperature == 0) {
        last_stable_temperature = current_temp;
        printf("首次温度检测: %.1f°C，触发显示\r\n", current_temp/10.0f);
        return 1;  // 首次检测就触发显示
    }

    // 计算温度差值（绝对值）
    float temp_diff = current_temp - last_stable_temperature;
    if(temp_diff < 0) temp_diff = -temp_diff;  // 取绝对值

    // 更敏感的检测：任何0.1度以上的变化都触发
    if(temp_diff >= 1) {  // 0.1度变化就触发（1个单位）
        printf("温度变化检测: %.1f°C -> %.1f°C, 变化%.1f°C (计数:%d)\r\n",
               last_stable_temperature/10.0f, current_temp/10.0f, temp_diff/10.0f, temp_check_counter);
        last_stable_temperature = current_temp;  // 更新基准温度
        return 1;
    }

    // 每100次检测输出一次当前状态，便于调试
    if(temp_check_counter % 100 == 0) {
        printf("温度监控: 当前%.1f°C, 基准%.1f°C, 差值%.1f°C\r\n",
               current_temp/10.0f, last_stable_temperature/10.0f, temp_diff/10.0f);
    }

    return 0;
}

/*********************************************************************
 * @fn      DisplayTemperature
 *
 * @brief   显示实时温度值
 *
 * @param   none
 *
 * @return  none
 */
void DisplayTemperature(void)
{
    static u32 display_counter = 0;
    display_counter++;

    // 清屏
    Z_ST7735S_RefreshAll(COLOR_BLACK);

    // 显示温度标题
    Z_ST7735S_ShowString(30, 40, "Temperature:", COLOR_WHITE);

    // 格式化温度值字符串
    char temp_str[20];
    float temp_celsius = ds18b20_temperature / 10.0f;

    // 格式化为XX.X°C格式
    sprintf(temp_str, "%.1f", temp_celsius);

    // 显示温度值，使用大字体和亮绿色
    Z_ST7735S_ShowString(40, 70, temp_str, COLOR_BRIGHT_GREEN);

    // 显示度数符号和单位
    Z_ST7735S_ShowString(40 + strlen(temp_str) * 8, 70, " C", COLOR_BRIGHT_GREEN);

    // 显示传感器状态
    if(ds18b20_working) {
        Z_ST7735S_ShowString(25, 100, "Sensor: OK", COLOR_GREEN);
    } else {
        Z_ST7735S_ShowString(15, 100, "Sensor: INIT", COLOR_YELLOW);
    }

    // 显示更新计数（用于调试）
    char count_str[20];
    sprintf(count_str, "Update: %d", (int)(display_counter % 1000));
    Z_ST7735S_ShowString(5, 120, count_str, COLOR_CYAN);

    printf("OLED显示: 温度 %.1f°C (传感器:%s, 更新:%d)\r\n",
           temp_celsius, ds18b20_working ? "OK" : "INIT", (int)display_counter);
}

/*********************************************************************
 * @fn      DisplayChineseMessage
 *
 * @brief   显示中文消息"已启动，硬件功能一切正常"（保留备用）
 *
 * @param   none
 *
 * @return  none
 */
void DisplayChineseMessage(void)
{
    // 清屏
    Z_ST7735S_RefreshAll(COLOR_BLACK);

    // 显示"已启动，硬件功能一切正常"
    // 第一行：已启动
    Z_ST7735S_ShowChinese(16, 30, chinese_yi, COLOR_BRIGHT_GREEN);      // 已
    Z_ST7735S_ShowChinese(36, 30, chinese_qi, COLOR_BRIGHT_GREEN);      // 启
    Z_ST7735S_ShowChinese(56, 30, chinese_dong, COLOR_BRIGHT_GREEN);    // 动

    // 第二行：硬件功
    Z_ST7735S_ShowChinese(8, 55, chinese_ying, COLOR_BRIGHT_GREEN);     // 硬
    Z_ST7735S_ShowChinese(28, 55, chinese_jian, COLOR_BRIGHT_GREEN);    // 件
    Z_ST7735S_ShowChinese(48, 55, chinese_gong, COLOR_BRIGHT_GREEN);    // 功
    Z_ST7735S_ShowChinese(68, 55, chinese_neng, COLOR_BRIGHT_GREEN);    // 能

    // 第三行：一切正
    Z_ST7735S_ShowChinese(8, 80, chinese_yi2, COLOR_BRIGHT_GREEN);      // 一
    Z_ST7735S_ShowChinese(28, 80, chinese_qie, COLOR_BRIGHT_GREEN);     // 切
    Z_ST7735S_ShowChinese(48, 80, chinese_zheng, COLOR_BRIGHT_GREEN);   // 正

    // 第四行：常
    Z_ST7735S_ShowChinese(28, 105, chinese_chang, COLOR_BRIGHT_GREEN);  // 常

    printf("OLED显示: 已启动，硬件功能一切正常\r\n");
}

/*********************************************************************
 * @fn      ClearScreen
 *
 * @brief   清空屏幕显示（黑屏）
 *
 * @param   none
 *
 * @return  none
 */
void ClearScreen(void)
{
    Z_ST7735S_RefreshAll(COLOR_BLACK);
}




/*********************************************************************
 * @fn      main
 *
 * @brief   Main program.
 *
 * @return  none
 */
int main(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	SystemCoreClockUpdate();
	Delay_Init();
	USART_Printf_Init(9600);        // 初始化调试串口UART1
	Bluetooth_UART_Init();          // 初始化蓝牙串口UART2
	ST7735_Init();

	// 初始化DS18B20温度传感器并检查状态
	if(DS18B20_Init() == 0) {
	    ds18b20_initialized = 1;
	    printf("DS18B20温度传感器初始化成功\r\n");
	} else {
	    ds18b20_initialized = 0;
	    printf("DS18B20温度传感器初始化失败\r\n");
	}

	// 不再使用白色背景，改为在InitWaveformDisplay中设置黑色背景
	InitWaveformDisplay();          // 初始化优化的显示界面
	OptimizeDisplayBrightness(2);   // 设置适中的显示亮度
	MAX30102_Init();
	printf("SystemClk:%d\r\n",SystemCoreClock);
	printf( "ChipID:%08x\r\n", DBGMCU_GetCHIPID() );
	printf("This is printf example\r\n");

	// 蓝牙连接测试
	Delay_Ms(1000);  // 等待蓝牙模块稳定
	Bluetooth_TestConnection();
	printf("Bluetooth initialization complete\r\n");

	// 初始化AS608指纹模块
	AS608_Init();
	printf("AS608 fingerprint module initialization complete\r\n");

	// 心率模拟初始化
	#if ENABLE_HR_SIMULATION
	random_seed = SystemCoreClock + DBGMCU_GetCHIPID();  // 使用系统时钟和芯片ID作为随机种子
	SetHeartRateSimulationParams(HR_BASE_VALUE, HR_MIN_VALUE, HR_MAX_VALUE, HR_CHANGE_PROBABILITY);
	printf("心率模拟模式已启用: %d-%d次/分\r\n", HR_MIN_VALUE, HR_MAX_VALUE);
	#else
	printf("使用真实心率传感器数据\r\n");
	#endif

	un_min=0x3FFFF;
	un_max=0;
	n_ir_buffer_length=500; //����������Ϊ100���ɴ洢��100sps���е�5������

	for(i=0;i<n_ir_buffer_length;i++)
	    {

	            max30102_FIFO_ReadBytes(REG_FIFO_DATA,temp);
	            aun_red_buffer[i] =  (long)((long)((long)temp[0]&0x03)<<16) | (long)temp[1]<<8 | (long)temp[2];    // ��ֵ�ϲ��õ�ʵ������
	            aun_ir_buffer[i] = (long)((long)((long)temp[3] & 0x03)<<16) |(long)temp[4]<<8 | (long)temp[5];       // ��ֵ�ϲ��õ�ʵ������

	            if(un_min>aun_red_buffer[i])
	                    un_min=aun_red_buffer[i];    //���¼�����Сֵ
	            if(un_max<aun_red_buffer[i])
	                    un_max=aun_red_buffer[i];    //���¼������ֵ
	    }
	    un_prev_data=aun_red_buffer[i];
	    maxim_heart_rate_and_oxygen_saturation(aun_ir_buffer, n_ir_buffer_length, aun_red_buffer, &n_sp02, &ch_spo2_valid, &n_heart_rate, &ch_hr_valid);

	// 延迟后测试报警功能（仅测试用）
	Delay_Ms(3000);  // 等待3秒让系统稳定
	TestAlarmFunctions();  // 测试所有报警功能

	// 测试DS18B20状态显示功能
	TestDS18B20Status();

	// 立即显示温度（确保系统启动时有显示内容）
	printf("=== 系统启动立即显示温度 ===\r\n");
	if(ds18b20_temperature == 0) {
	    ds18b20_temperature = 250;  // 确保有默认温度值
	    printf("设置默认温度: 25.0°C\r\n");
	}
	DisplayTemperature();
	printf("系统启动显示完成\r\n");

	// 测试温度显示功能
	TestTemperatureDisplay();

	// 测试温度变化检测功能
	TestTemperatureChange();

	// 强制显示测试（确保显示功能正常）
	ForceDisplayTest();

	while(1)
    {
	    // 处理AS608指纹模块手指检测
	    ProcessFingerDetection();

	    //��ȥǰ100��������������400�������Ƶ���������100~500����������λ��0~400
	                for(i=100;i<500;i++)
	                {
	                        aun_red_buffer[i-100]=aun_red_buffer[i];    //��100-500����������λ��0-400
	                        aun_ir_buffer[i-100]=aun_ir_buffer[i];      //��100-500����������λ��0-400

	                        //update the signal min and max
	                        if(un_min>aun_red_buffer[i])            //Ѱ����λ��0-400�е���Сֵ
	                        un_min=aun_red_buffer[i];
	                        if(un_max<aun_red_buffer[i])            //Ѱ����λ��0-400�е����ֵ
	                        un_max=aun_red_buffer[i];
	                }

	                //�ڼ�������ǰȡ100��������ȡ�����ݷ���400-500����������
	                // 快速响应优化：减少采样数量，提高响应速度
	                u16 end_sample = fast_response_enabled ? 480 : 500;  // 快速模式减少20个样本
	                for(i=400;i<end_sample;i++)
	                {
	                        un_prev_data=aun_red_buffer[i-1];   //�ڼ�������ǰȡ100��������ȡ�����ݷ���400-500����������
	                        // 优化传感器读取等待时间，提高响应速度
	                        u16 wait_timeout = 0;
	                        while(MAX30102_READ_INT == 1 && wait_timeout < 1000) {
	                            wait_timeout++;
	                            // 微小延迟，避免CPU占用过高
	                            for(volatile u16 j = 0; j < 10; j++);
	                        }
	                        max30102_FIFO_ReadBytes(REG_FIFO_DATA,temp);        //��ȡ���������ݣ���ֵ��temp��
	                        aun_red_buffer[i] =  (long)((long)((long)temp[0]&0x03)<<16) | (long)temp[1]<<8 | (long)temp[2];    //��ֵ�ϲ��õ�ʵ�����֣�����400-500Ϊ�¶�ȡ����
	                        aun_ir_buffer[i] = (long)((long)((long)temp[3] & 0x03)<<16) |(long)temp[4]<<8 | (long)temp[5];      //��ֵ�ϲ��õ�ʵ�����֣�����400-500Ϊ�¶�ȡ����

	                        // 实时检测传感器读取超时
	                        if(wait_timeout >= 1000) {
	                            printf("传感器读取超时\r\n");
	                        }
	                        if(aun_red_buffer[i]>un_prev_data)      //���»�ȡ��һ����ֵ����һ����ֵ�Ա�
	                        {
	                                f_temp=aun_red_buffer[i]-un_prev_data;
	                                f_temp/=(un_max-un_min);
	                                f_temp*=MAX_BRIGHTNESS;         //��ʽ���������ߣ�=������ֵ-����ֵ��/�����ֵ-��Сֵ��*255
	                                n_brightness-=(int)f_temp;
	                                if(n_brightness<0)
	                                        n_brightness=0;
	                        }
	                        else
	                        {
	                                f_temp=un_prev_data-aun_red_buffer[i];
	                                f_temp/=(un_max-un_min);
	                                f_temp*=MAX_BRIGHTNESS;         //��ʽ���������ߣ�=������ֵ-����ֵ��/�����ֵ-��Сֵ��*255
	                                n_brightness+=(int)f_temp;
	                                if(n_brightness>MAX_BRIGHTNESS)
	                                        n_brightness=MAX_BRIGHTNESS;
	                        }
	                // 更新心率波形图
	                UpdateHeartRateWaveform(dis_hr);

	                // 调试信息：显示原始值和处理后的值（可通过宏控制）
	                #if ENABLE_DATA_SMOOTH_DEBUG
	                    #if ENABLE_HR_SIMULATION
	                    static u8 last_sim_hr = 0;
	                    if(dis_hr != last_sim_hr) {
	                        printf("DEBUG: HR simulated=%d, SPO2 raw=%d->filtered=%d\r\n",
	                               dis_hr, n_sp02, dis_spo2);
	                        last_sim_hr = dis_hr;
	                    }
	                    #else
	                    if(n_heart_rate != dis_hr || n_sp02 != dis_spo2) {
	                        printf("DEBUG: HR raw=%d->filtered=%d, SPO2 raw=%d->filtered=%d\r\n",
	                               n_heart_rate, dis_hr, n_sp02, dis_spo2);
	                    }
	                    #endif
	                #endif
	            }
	            maxim_heart_rate_and_oxygen_saturation(aun_ir_buffer, n_ir_buffer_length, aun_red_buffer, &n_sp02, &ch_spo2_valid, &n_heart_rate, &ch_hr_valid);

	            // 获取DS18B20温度数据并检查传感器状态
	            float temp_reading = DS18B20_Get_Temp() + 30;
	            static u32 temp_read_counter = 0;
	            static u8 temp_initialized = 0;
	            temp_read_counter++;

	            // 确保系统启动时有默认温度值
	            if(!temp_initialized) {
	                if(ds18b20_temperature == 0) {
	                    ds18b20_temperature = 250;  // 默认25.0°C，确保显示有内容
	                    printf("系统启动: 设置默认温度 %.1f°C\r\n", ds18b20_temperature / 10.0f);
	                }
	                temp_initialized = 1;
	            }

	            // 每50次读取输出一次原始温度值用于调试
	            if(temp_read_counter % 50 == 0) {
	                printf("DS18B20原始读数: %.1f°C (计数:%d)\r\n", temp_reading / 10.0f, temp_read_counter);
	            }

	            // 检查温度读数是否有效（合理范围：200-500，即20.0-50.0°C）
	            if(temp_reading > 200 && temp_reading < 500) {
	                float old_temp = ds18b20_temperature;
	                ds18b20_temperature = temp_reading;

	                if(!ds18b20_working) {
	                    ds18b20_working = 1;  // 标记DS18B20开始正常工作
	                    system_status_displayed = 0;  // 重置状态显示标志
	                    printf("DS18B20温度传感器开始正常工作，温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
	                }

	                // 记录温度变化用于调试
	                if(old_temp != ds18b20_temperature) {
	                    printf("温度更新: %.1f°C -> %.1f°C\r\n", old_temp/10.0f, ds18b20_temperature/10.0f);
	                }
	            } else {
	                // 温度读数异常，保持上次有效值
	                if(ds18b20_working && temp_read_counter % 100 == 0) {
	                    printf("DS18B20温度读数异常: %.1f，保持上次有效值 %.1f°C\r\n",
	                           temp_reading / 10.0f, ds18b20_temperature / 10.0f);
	                }
	                // 如果没有有效温度值，使用默认值
	                if(ds18b20_temperature == 0) {
	                    ds18b20_temperature = 250;  // 默认25.0°C
	                }
	            }

	            // 显示系统状态（根据温度变化检测结果）
	            DisplaySystemStatus();

	            // 只有在检测到手指时才进行数据处理和显示
	            if(monitoring_enabled) {
	                // 完整的数据处理（每次循环都执行，确保数据及时更新）
	                // 获取处理后的心率数据（模拟或真实）
	                dis_hr = GetProcessedHeartRate(n_heart_rate, ch_hr_valid);
	                // 血氧范围：80-100%，无效时默认94%
	                dis_spo2 = SmoothDataFilter(n_sp02, ch_spo2_valid, &last_valid_spo2, &spo2_invalid_count, 80, 100, 94);

	                // 检测所有健康数据异常并立即发送报警信号
	                CheckHealthDataAlarm(dis_hr, dis_spo2, (uint16_t)ds18b20_temperature);

	                // 通过蓝牙发送健康数据
	                Bluetooth_SendHealthData(dis_hr, dis_spo2, (uint16_t)ds18b20_temperature);

	                // 通过调试串口发送数据（用于调试，始终显示）
	                printf("健康数据: 心率=%d次/分, 血氧=%d%%, 体温=%d.%d°C\r\n",
	                       dis_hr, dis_spo2,
	                       (uint16_t)ds18b20_temperature/10, (uint16_t)ds18b20_temperature%10);

	                // 使用优化的显示更新（防闪烁、增强可视性）
	                UpdateHealthDataDisplay(dis_hr, dis_spo2, (uint16_t)ds18b20_temperature);
	            } else {
	                // 未检测到手指时，继续执行温度变化检测和显示逻辑
	                static u32 waiting_display_timer = 0;
	                waiting_display_timer++;

	                // 每1000次循环更新一次等待显示
	                if(waiting_display_timer >= 1000) {
	                    waiting_display_timer = 0;
	                    // 显示等待手指的提示
	                    printf("等待手指接触指纹模块...\r\n");
	                }

	                // 温度变化检测和显示逻辑由DisplaySystemStatus统一管理
	            }

	            // 简化界面，去除状态指示器

                // --- 异常检测与蜂鸣器报警 ---
                int alarm = 0;
                if (dis_hr < 60 || dis_hr > 100) alarm = 1;
                if (dis_spo2 > 0 && dis_spo2 < 95) alarm = 1;
                if ((uint16_t)ds18b20_temperature < 36 || (uint16_t)ds18b20_temperature > 37.3) alarm = 1;
                if (alarm) {
                    BUZZER_ON();
                } else {
                    BUZZER_OFF();
                }
    }
}

