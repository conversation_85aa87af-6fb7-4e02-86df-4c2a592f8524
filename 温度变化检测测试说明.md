# 温度变化检测功能测试说明

## 🧪 测试目的

验证DS18B20温度变化检测功能是否正常工作，确保：
1. 温度变化超过1度时能正确触发显示
2. 平常保持黑屏状态
3. 消息显示3秒后自动清屏

## 🔬 测试方法

### 方法1：自然温度变化测试
1. **环境准备**: 将设备放置在室温环境中
2. **初始状态**: 观察屏幕应为黑屏状态
3. **温度变化**: 用手指接触DS18B20传感器，或使用热源/冷源
4. **观察结果**: 当温度变化超过1度时，屏幕应显示中文消息
5. **自动清屏**: 3秒后屏幕应自动恢复黑屏

### 方法2：串口监控测试
1. **连接串口**: 使用串口调试工具连接设备
2. **观察输出**: 查看温度变化检测的调试信息
3. **验证逻辑**: 确认温度变化计算和显示触发逻辑正确

## 📊 预期结果

### 正常工作流程
```
1. 系统启动 → 黑屏状态
2. 温度变化 < 1度 → 保持黑屏
3. 温度变化 ≥ 1度 → 显示中文消息
4. 显示3秒 → 自动清屏
5. 恢复黑屏状态
```

### 串口输出示例
```
DS18B20温度传感器开始正常工作，温度: 25.3°C
温度变化检测: 25.3°C -> 26.5°C, 变化1.2°C
检测到温度变化超过1度，准备显示消息
系统状态: 温度变化检测到，显示启动消息
OLED显示: 已启动，硬件功能一切正常
系统状态: 消息显示完毕，恢复黑屏
```

## 🎯 关键测试点

### 1. 温度变化阈值测试
- **测试**: 温度变化0.5度 → 应保持黑屏
- **测试**: 温度变化1.0度 → 应显示消息
- **测试**: 温度变化1.5度 → 应显示消息

### 2. 显示时间测试
- **测试**: 消息显示后计时3秒
- **验证**: 3秒后自动清屏恢复黑屏

### 3. 重复触发测试
- **测试**: 连续多次温度变化
- **验证**: 每次变化超过1度都能正确触发显示

### 4. 异常情况测试
- **测试**: DS18B20传感器断开
- **验证**: 系统应保持稳定，不崩溃

## 🔧 调试技巧

### 1. 串口调试
```c
// 在代码中添加调试输出
printf("当前温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
printf("基准温度: %.1f°C\r\n", last_stable_temperature / 10.0f);
printf("温度变化: %.1f°C\r\n", temp_diff / 10.0f);
```

### 2. 手动触发测试
```c
// 在TestTemperatureChange()函数中强制触发
temperature_change_detected = 1;
DisplaySystemStatus();
```

### 3. 调整参数测试
```c
// 临时修改阈值进行测试
#define TEMP_CHANGE_THRESHOLD 5  // 0.5度阈值
#define MESSAGE_DISPLAY_DURATION 1000  // 1秒显示时间
```

## ✅ 验收标准

### 功能验收
- [ ] 系统启动后屏幕为黑屏状态
- [ ] 温度变化小于1度时保持黑屏
- [ ] 温度变化大于等于1度时显示中文消息
- [ ] 中文消息显示完整且清晰
- [ ] 消息显示3秒后自动清屏
- [ ] 清屏后恢复黑屏状态

### 性能验收
- [ ] 温度检测响应时间 < 1秒
- [ ] 显示切换无闪烁现象
- [ ] 系统运行稳定，无死机重启
- [ ] 串口调试信息输出正常

### 用户体验验收
- [ ] 中文字体清晰可读
- [ ] 显示位置居中美观
- [ ] 颜色搭配合理（绿色字体）
- [ ] 黑屏状态节能效果明显

---

**测试完成后请确认所有验收标准都已满足**
