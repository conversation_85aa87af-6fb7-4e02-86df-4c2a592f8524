# DS18B20温度变化检测与中文状态显示功能

## 🎯 功能概述

根据您的最新要求，当DS18B20温度传感器感受到温度变化超过1度时，在硬件OLED屏幕上显示中文"已启动，硬件功能一切正常"。平常保持黑屏状态（屏幕上无内容），只有在检测到温度变化时才显示消息。

## 🔧 实现的功能

### 1. 状态管理变量
在main.c中添加了以下全局变量来管理DS18B20状态和温度变化检测：

```c
// DS18B20温度传感器状态管理
u8 ds18b20_initialized = 0;       // DS18B20初始化状态标志
u8 ds18b20_working = 0;           // DS18B20工作状态标志
u8 system_status_displayed = 0;   // 系统状态显示标志
u32 status_display_timer = 0;     // 状态显示计时器

// 温度变化检测相关变量
float last_stable_temperature = 0;    // 上次稳定温度值
u8 temperature_change_detected = 0;   // 温度变化检测标志
u32 message_display_timer = 0;        // 消息显示计时器
u8 message_displayed = 0;             // 消息显示状态标志
#define TEMP_CHANGE_THRESHOLD 10      // 温度变化阈值（1度 = 10个单位）
#define MESSAGE_DISPLAY_DURATION 3000 // 消息显示持续时间（3秒）
```

### 2. 温度变化检测函数
新增了`CheckTemperatureChange()`函数，用于检测温度变化是否超过1度：

```c
u8 CheckTemperatureChange(float current_temp)
{
    // 如果是第一次检测，记录初始温度
    if(last_stable_temperature == 0) {
        last_stable_temperature = current_temp;
        return 0;
    }

    // 计算温度差值（绝对值）
    float temp_diff = current_temp - last_stable_temperature;
    if(temp_diff < 0) temp_diff = -temp_diff;  // 取绝对值

    // 检查是否超过1度阈值
    if(temp_diff >= TEMP_CHANGE_THRESHOLD) {
        printf("温度变化检测: %.1f°C -> %.1f°C, 变化%.1f°C\r\n",
               last_stable_temperature/10.0f, current_temp/10.0f, temp_diff/10.0f);
        last_stable_temperature = current_temp;  // 更新基准温度
        return 1;
    }

    return 0;
}
```

### 3. 中文字体数据
添加了11个常用汉字的16x16像素字体数据：

```c
// 中文字体数据 16x16像素
const unsigned char chinese_yi[32] = {...};    // "已"
const unsigned char chinese_qi[32] = {...};    // "启"
const unsigned char chinese_dong[32] = {...};  // "动"
const unsigned char chinese_ying[32] = {...};  // "硬"
const unsigned char chinese_jian[32] = {...};  // "件"
const unsigned char chinese_gong[32] = {...};  // "功"
const unsigned char chinese_neng[32] = {...};  // "能"
const unsigned char chinese_yi2[32] = {...};   // "一"
const unsigned char chinese_qie[32] = {...};   // "切"
const unsigned char chinese_zheng[32] = {...}; // "正"
const unsigned char chinese_chang[32] = {...}; // "常"
```

### 3. 中文字符显示函数
新增了`Z_ST7735S_ShowChinese()`函数，用于显示16x16中文字符：

```c
void Z_ST7735S_ShowChinese(uint8_t x, uint8_t y, const unsigned char *font_data, uint16_t rgb)
{
    Z_ST7735S_SpecifyScope(x, x+15, y, y+15);  // 16x16像素
    for(uint8_t i = 0; i < 32; i++) {  // 32字节数据
        uint8_t temp = font_data[i];
        for(uint8_t j = 0; j < 8; j++) {
            if((temp & 0x80) != 0) {
                Z_ST7735S_Send16bitsRGB(rgb);
            } else {
                Z_ST7735S_Send16bitsRGB(COLOR_BLACK);
            }
            temp <<= 1;
        }
    }
}
```

### 4. 中文消息显示函数
新增了`DisplayChineseMessage()`函数，专门显示"已启动，硬件功能一切正常"：

```c
void DisplayChineseMessage(void)
{
    // 清屏
    Z_ST7735S_RefreshAll(COLOR_BLACK);

    // 显示"已启动，硬件功能一切正常"
    // 第一行：已启动
    Z_ST7735S_ShowChinese(16, 30, chinese_yi, COLOR_BRIGHT_GREEN);      // 已
    Z_ST7735S_ShowChinese(36, 30, chinese_qi, COLOR_BRIGHT_GREEN);      // 启
    Z_ST7735S_ShowChinese(56, 30, chinese_dong, COLOR_BRIGHT_GREEN);    // 动

    // 第二行：硬件功
    Z_ST7735S_ShowChinese(8, 55, chinese_ying, COLOR_BRIGHT_GREEN);     // 硬
    Z_ST7735S_ShowChinese(28, 55, chinese_jian, COLOR_BRIGHT_GREEN);    // 件
    Z_ST7735S_ShowChinese(48, 55, chinese_gong, COLOR_BRIGHT_GREEN);    // 功
    Z_ST7735S_ShowChinese(68, 55, chinese_neng, COLOR_BRIGHT_GREEN);    // 能

    // 第三行：一切正
    Z_ST7735S_ShowChinese(8, 80, chinese_yi2, COLOR_BRIGHT_GREEN);      // 一
    Z_ST7735S_ShowChinese(28, 80, chinese_qie, COLOR_BRIGHT_GREEN);     // 切
    Z_ST7735S_ShowChinese(48, 80, chinese_zheng, COLOR_BRIGHT_GREEN);   // 正

    // 第四行：常
    Z_ST7735S_ShowChinese(28, 105, chinese_chang, COLOR_BRIGHT_GREEN);  // 常

    printf("OLED显示: 已启动，硬件功能一切正常\r\n");
}
```

### 5. 黑屏控制函数
新增了`ClearScreen()`函数，用于清空屏幕显示：

```c
void ClearScreen(void)
{
    Z_ST7735S_RefreshAll(COLOR_BLACK);
}
```

### 6. 系统状态显示函数
重新设计了`DisplaySystemStatus()`函数，实现基于温度变化的智能显示：

```c
void DisplaySystemStatus(void)
{
    // 检查是否需要显示消息
    if(temperature_change_detected && !message_displayed) {
        // 温度变化超过1度，显示中文消息
        DisplayChineseMessage();
        message_displayed = 1;
        message_display_timer = 0;
        printf("系统状态: 温度变化检测到，显示启动消息\r\n");
    } else if(message_displayed) {
        // 消息正在显示中，检查是否需要清屏
        message_display_timer++;
        if(message_display_timer >= MESSAGE_DISPLAY_DURATION) {
            // 显示时间到，清屏
            ClearScreen();
            message_displayed = 0;
            temperature_change_detected = 0;
            message_display_timer = 0;
            printf("系统状态: 消息显示完毕，恢复黑屏\r\n");
        }
    } else {
        // 平常状态，保持黑屏
        static u8 black_screen_set = 0;
        if(!black_screen_set) {
            ClearScreen();
            black_screen_set = 1;
            printf("系统状态: 保持黑屏状态\r\n");
        }
    }
}
```

### 3. 初始化状态检测
在系统初始化时检测DS18B20状态：

```c
// 初始化DS18B20温度传感器并检查状态
if(DS18B20_Init() == 0) {
    ds18b20_initialized = 1;
    printf("DS18B20温度传感器初始化成功\r\n");
} else {
    ds18b20_initialized = 0;
    printf("DS18B20温度传感器初始化失败\r\n");
}
```

### 4. 实时温度检测与状态更新
在主循环中实时检测温度并更新状态：

```c
// 获取DS18B20温度数据并检查传感器状态
float temp_reading = DS18B20_Get_Temp() + 30;

// 检查温度读数是否有效（合理范围：200-500，即20.0-50.0°C）
if(temp_reading > 200 && temp_reading < 500) {
    ds18b20_temperature = temp_reading;
    if(!ds18b20_working) {
        ds18b20_working = 1;  // 标记DS18B20开始正常工作
        system_status_displayed = 0;  // 重置状态显示标志
        printf("DS18B20温度传感器开始正常工作，温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
    }
} else {
    // 温度读数异常，保持上次有效值或设置默认值
    if(ds18b20_working) {
        printf("DS18B20温度读数异常: %.1f，保持上次有效值\r\n", temp_reading / 10.0f);
    }
    if(ds18b20_temperature == 0) {
        ds18b20_temperature = 365;  // 默认36.5°C
    }
}

// 显示系统状态（仅在状态改变或定时更新时）
status_display_timer++;
if(!system_status_displayed || status_display_timer > 5000) {  // 每5000次循环更新一次状态显示
    DisplaySystemStatus();
    system_status_displayed = 1;
    status_display_timer = 0;
}
```

### 5. 测试功能
添加了测试函数`TestDS18B20Status()`用于调试：

```c
void TestDS18B20Status(void)
{
    printf("=== DS18B20状态测试 ===\r\n");
    printf("DS18B20初始化状态: %s\r\n", ds18b20_initialized ? "成功" : "失败");
    printf("DS18B20工作状态: %s\r\n", ds18b20_working ? "正常" : "等待");
    printf("当前温度: %.1f°C\r\n", ds18b20_temperature / 10.0f);
    printf("状态显示标志: %s\r\n", system_status_displayed ? "已显示" : "未显示");
    printf("========================\r\n");
}
```

## 📱 显示效果

### 平常状态（默认）
- **黑屏**: 屏幕完全黑色，无任何内容显示
- **节能**: 减少屏幕功耗，延长设备使用寿命

### 温度变化检测状态
当DS18B20检测到温度变化超过1度时：
```
已启动          (绿色，第一行)
硬件功能        (绿色，第二行)
一切正          (绿色，第三行)
常              (绿色，第四行)
```
- **显示时间**: 3秒钟
- **自动清屏**: 3秒后自动恢复黑屏状态

### 字符布局
- **字体大小**: 16x16像素
- **字符间距**: 4像素
- **行间距**: 25像素
- **居中显示**: 自动计算居中位置
- **颜色**: 亮绿色（COLOR_BRIGHT_GREEN）

## 🔍 工作原理

1. **初始化检测**: 系统启动时检测DS18B20是否初始化成功
2. **温度有效性验证**: 检查温度读数是否在合理范围内（20.0-50.0°C）
3. **状态标志管理**: 使用标志位跟踪传感器工作状态
4. **定时显示更新**: 每5000次循环或状态改变时更新显示
5. **异常处理**: 温度读数异常时保持上次有效值

## 🎨 显示位置

- **显示区域**: 屏幕中央位置 (10, 80) 开始，108×40像素区域
- **颜色方案**: 
  - 正常状态: 绿色文字
  - 温度数值: 蓝色文字
  - 等待状态: 黄色文字
- **字体**: 系统默认8×16像素字体

## 🔧 技术特点

1. **智能温度变化检测**: 精确检测温度变化是否超过1度阈值
2. **中文字体支持**: 自定义16x16像素中文字体数据
3. **节能黑屏模式**: 平常保持黑屏状态，减少功耗
4. **定时显示控制**: 消息显示3秒后自动清屏
5. **专一显示**: 屏幕只显示中文状态消息，不显示其他数据
6. **防闪烁**: 使用状态标志避免频繁刷新屏幕
7. **异常处理**: 完善的异常检测和恢复机制
8. **调试支持**: 提供详细的串口调试信息
9. **资源优化**: 智能显示控制减少CPU占用
10. **字体优化**: 16x16像素中文字符，清晰可读

## 📋 使用说明

1. **系统启动**: 系统启动后屏幕保持黑屏状态
2. **温度变化触发**: 当环境温度变化超过1度时，屏幕自动显示中文消息
3. **自动清屏**: 消息显示3秒后自动恢复黑屏状态
4. **状态监控**: 通过串口可以看到详细的温度变化和显示状态信息
5. **节能模式**: 平常黑屏状态有效节省电能消耗

## 🚀 扩展功能

该功能为后续扩展预留了接口：
- 可以添加更多传感器状态检测
- 可以自定义显示内容和样式
- 可以添加声音提示功能
- 可以记录状态变化历史

## 🎯 重要更新

### v3.0 更新内容（最新）
1. **智能温度变化检测**: 只有温度变化超过1度时才显示消息
2. **节能黑屏模式**: 平常保持黑屏状态，节省电能
3. **定时显示控制**: 消息显示3秒后自动清屏
4. **温度变化阈值**: 精确的1度变化检测算法
5. **显示逻辑重构**: 完全重新设计的显示控制系统

### v2.0 更新内容
1. **新增中文字体支持**: 添加11个汉字的16x16像素字体数据
2. **专一显示模式**: 屏幕只显示"已启动，硬件功能一切正常"中文消息
3. **显示逻辑优化**: 修改所有显示函数，确保只显示中文状态信息
4. **测试功能增强**: 新增中文显示测试函数

### 核心改进
- ✅ **智能检测**: 温度变化超过1度才触发显示
- ✅ **节能设计**: 平常黑屏状态，减少功耗
- ✅ **定时控制**: 3秒显示后自动清屏
- ✅ **中文显示**: 完整的"已启动，硬件功能一切正常"中文显示
- ✅ **专一界面**: 屏幕只显示该文字，无其他内容
- ✅ **字体清晰**: 16x16像素中文字体，清晰可读
- ✅ **布局优化**: 多行居中显示，美观整洁

---

**© 2024 智能医疗监测系统 | DS18B20温度变化检测与中文状态显示功能 v3.0**
